name: E2E Tests

on:
  pull_request:
    branches:
      - development
      - QA
      - UAT
      - Demo
      - Prod
    types: [opened, synchronize, reopened]

jobs:
  e2e-tests:
    name: Run E2E Tests
    runs-on: ubuntu-latest
    
    services:
      mongodb-main:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: testuser
          MONGO_INITDB_ROOT_PASSWORD: testpass
          MONGO_INITDB_DATABASE: coddn_test
        ports:
          - 27018:27017
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand({ping: 1})'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      mongodb-spectrum:
        image: mongo:7.0
        env:
          MONGO_INITDB_ROOT_USERNAME: testuser
          MONGO_INITDB_ROOT_PASSWORD: testpass
          MONGO_INITDB_DATABASE: coddn_spectrum_test
        ports:
          - 27019:27017
        options: >-
          --health-cmd "mongosh --eval 'db.runCommand({ping: 1})'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Wait for MongoDB services
        run: |
          echo "Waiting for MongoDB services to be ready..."
          timeout 60 bash -c 'until nc -z localhost 27018; do sleep 1; done'
          timeout 60 bash -c 'until nc -z localhost 27019; do sleep 1; done'
          echo "MongoDB services are ready!"

      - name: Initialize MongoDB databases
        run: |
          # Initialize main database
          mongosh "***********************************************************************" --eval "
            db.createCollection('test');
            db.test.insertOne({initialized: true});
            print('Main database initialized');
          "
          
          # Initialize spectrum database
          mongosh "********************************************************************************" --eval "
            db.createCollection('test');
            db.test.insertOne({initialized: true});
            print('Spectrum database initialized');
          "

      - name: Run E2E tests
        run: npm run test:e2e
        env:
          NODE_ENV: test
          MONGODB_URI: ***********************************************************************
          MONGODB_SPECTRUM_URI: ********************************************************************************

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-test-results
          path: |
            test-results/
            coverage/
          retention-days: 7
