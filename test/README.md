# E2E Testing Setup

This directory contains the complete end-to-end (e2e) testing setup for the CoDDN API, including Docker MongoDB containers, database seeding, and comprehensive test utilities.

## Overview

The e2e testing framework provides:

- **Docker MongoDB containers** for isolated testing environments
- **Automated database seeding** with realistic test data
- **Test utilities** for setup, teardown, and data management
- **Complete test isolation** with clean state for each test

## Prerequisites

- Docker and Docker Compose installed
- Node.js and npm/yarn
- All project dependencies installed (`npm install`)

## Quick Start

### 1. Run E2E Tests

```bash
# Run all e2e tests
npm run test:e2e

# Run e2e tests in watch mode
npm run test:e2e:watch

# Run e2e tests with debugging
npm run test:e2e:debug
```

### 2. Manual Docker Management

```bash
# Start test containers manually
npm run docker:test:up

# Stop test containers
npm run docker:test:down

# View container logs
npm run docker:test:logs
```

## Architecture

### Docker Setup

- **Main MongoDB**: `localhost:27018` - Primary database for application data
- **Spectrum MongoDB**: `localhost:27019` - Separate database for spectrum events
- **Automatic initialization** with proper indexes and collections
- **Health checks** to ensure containers are ready before tests run

### Test Structure

```
test/
├── database/                 # Test database modules
│   ├── test-database.module.ts
│   └── test-spectrum-database.module.ts
├── fixtures/                 # Test data factories
│   └── seed-data.factory.ts
├── utils/                    # Test utilities
│   ├── docker-manager.ts     # Docker container management
│   ├── database-seeder.ts    # Database seeding utilities
│   └── test-setup.ts         # Main test setup orchestrator
├── docker-compose.test.yml   # Docker configuration
├── init-mongo.js            # MongoDB initialization script
├── jest-e2e.json           # Jest configuration for e2e tests
├── jest.setup.e2e.ts       # Global test setup
└── *.e2e-spec.ts           # Individual test suites
```

## Writing E2E Tests

### Basic Test Structure

```typescript
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';

describe('YourModule (e2e)', () => {
  let context: E2ETestContext;
  let seededData: SeededData;

  beforeAll(async () => {
    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      YourModule,
      // ... other required modules
    ]);

    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);

    // Seed test data
    seededData = await TestSetup.seedTestData(context);
  }, 120000);

  afterAll(async () => {
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  it('should test your functionality', async () => {
    // Your test implementation
  });
});
```

### Available Seed Data

The `SeededData` interface provides:

- `organizations[]` - Test organizations
- `users[]` - Test users with different roles
- `serviceZones[]` - Service zones for geographic testing
- `alertZones[]` - Alert zones linked to organizations
- `drones[]` - Test drones with various configurations
- `droneAuthorizations[]` - Authorization records
- `sensors[]` - Sensor profiles for detection testing
- `eventProfiles[]` - Event data for testing workflows

## Configuration

### Environment Variables

The following environment variables are automatically set for tests:

- `NODE_ENV=test`
- `ENABLE_CACHE=false`
- `TEST_MONGODB_CONNECTION_STRING`
- `TEST_DATABASE_NAME`
- `TEST_SPECTRUM_MONGODB_CONNECTION_STRING`
- `TEST_SPECTRUM_DATABASE_NAME`

### Timeouts

- **Global timeout**: 120 seconds (2 minutes)
- **Container startup**: 60 seconds
- **Database connection**: 10 seconds

## Troubleshooting

### Common Issues

1. **Docker containers not starting**

   ```bash
   # Check Docker is running
   docker ps

   # View container logs
   npm run docker:test:logs

   # Force cleanup and restart
   npm run docker:test:down
   npm run docker:test:up
   ```

2. **Database connection issues**

   - Ensure containers are healthy before running tests
   - Check port conflicts (27018, 27019)
   - Verify MongoDB credentials in docker-compose.test.yml

3. **Test timeouts**
   - Increase timeout in jest-e2e.json if needed
   - Check for hanging database connections
   - Ensure proper test cleanup

### Debugging

```bash
# Run with verbose output
npm run test:e2e -- --verbose

# Run specific test file
npm run test:e2e -- alertZones.e2e-spec.ts

# Debug with Node inspector
npm run test:e2e:debug
```

## Authentication in E2E Tests

The application uses Auth0 for authentication, but e2e tests bypass Auth0 validation in test environments while maintaining the same request context structure.

### How Authentication Bypass Works

1. **Environment Detection**: The authentication middleware detects `NODE_ENV=test` and skips Auth0 JWT validation
2. **Test Headers**: Test requests use special headers to simulate user context:

   - `x-test-user-sub`: User's Auth0 subject ID
   - `x-test-org-id`: User's organization ID
   - `x-test-permissions`: Comma-separated list of permissions
   - `x-test-roles`: Comma-separated list of user roles

3. **Request Context**: The middleware populates the same `req['user']`, `req['sub']`, `req['org_id']`, etc. properties that real Auth0 requests would have

### Using the AuthHelper

```typescript
import { AuthHelper, TestUser } from './utils/auth-helper';

// Create different types of test users
const adminUser = AuthHelper.createAdminUser();
const readOnlyUser = AuthHelper.createReadOnlyUser();
const customUser = AuthHelper.createTestUser({
  org_id: 'specific-org-id',
  permissions: ['read:sensors', 'write:sensors'],
});

// Make authenticated requests
const response = await AuthHelper.get(app, '/api/sensors', adminUser);
const createResponse = await AuthHelper.post(app, '/api/sensors', sensorData, adminUser);
```

### Testing Authorization

```typescript
describe('Authorization Tests', () => {
  it('should allow access with correct permissions', async () => {
    const userWithPermission = AuthHelper.createTestUser({
      permissions: ['read:sensors'],
    });

    await AuthHelper.get(app, '/api/sensors', userWithPermission).expect(200);
  });

  it('should deny access without permissions', async () => {
    const userWithoutPermission = AuthHelper.createTestUser({
      permissions: ['read:other-resources'],
    });

    await AuthHelper.get(app, '/api/sensors', userWithoutPermission).expect(403);
  });
});
```

## Best Practices

1. **Test Isolation**: Each test should work with fresh seeded data
2. **Resource Cleanup**: Always use proper setup/teardown hooks
3. **Realistic Data**: Use the seed factories for consistent test data
4. **Error Handling**: Include tests for error conditions and edge cases
5. **Performance**: Keep tests focused and avoid unnecessary operations
6. **Authentication Testing**: Test both authorized and unauthorized scenarios
7. **Permission Boundaries**: Verify that users can only access resources they have permissions for
