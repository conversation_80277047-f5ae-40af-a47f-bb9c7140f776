import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { AdminModule } from '../src/domains/admin/admin.module';
import { AdminController } from '../src/domains/admin/admin.controller';
import { AdminService } from '../src/domains/admin/admin.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('Admin API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let regularUser: TestUser;
  let adminUser: TestUser;
  let superAdminUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Admin API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      AdminModule,
      UtilsModule
    ], [
      AdminController
    ], [
      AdminService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users with different admin levels
    regularUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['user']
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['admin'],
      permissions: ['admin:cache', 'admin:system']
    });

    superAdminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['super-admin', 'ad-admin'],
      permissions: ['admin:cache', 'admin:system', 'admin:all']
    });

    console.log('✅ Admin API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Admin API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('POST /api/admin/cache/clear', () => {
    it('should clear cache for admin user', async () => {
      const response = await AuthHelper.post(app, '/api/admin/cache/clear', {}, adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('cleared');
    });

    it('should deny cache clear for regular user', async () => {
      await AuthHelper.post(app, '/api/admin/cache/clear', {}, regularUser)
        .expect(403);
    });

    it('should allow cache clear for super admin', async () => {
      const response = await AuthHelper.post(app, '/api/admin/cache/clear', {}, superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('message');
    });
  });

  describe('POST /api/admin/cache/clear/:key', () => {
    it('should clear specific cache key for admin user', async () => {
      const cacheKey = 'test-cache-key';
      
      const response = await AuthHelper.post(app, `/api/admin/cache/clear/${cacheKey}`, {}, adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain(cacheKey);
    });

    it('should deny specific cache clear for regular user', async () => {
      const cacheKey = 'test-cache-key';
      
      await AuthHelper.post(app, `/api/admin/cache/clear/${cacheKey}`, {}, regularUser)
        .expect(403);
    });
  });

  describe('GET /api/admin/cache/stats', () => {
    it('should return cache statistics for admin user', async () => {
      const response = await AuthHelper.get(app, '/api/admin/cache/stats', adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('totalKeys');
      expect(response.body).toHaveProperty('memoryUsage');
      expect(response.body).toHaveProperty('hitRate');
      expect(response.body).toHaveProperty('missRate');
      expect(typeof response.body.totalKeys).toBe('number');
    });

    it('should deny cache stats for regular user', async () => {
      await AuthHelper.get(app, '/api/admin/cache/stats', regularUser)
        .expect(403);
    });
  });

  describe('GET /api/admin/system/health', () => {
    it('should return system health for admin user', async () => {
      const response = await AuthHelper.get(app, '/api/admin/system/health', adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('status');
      expect(response.body).toHaveProperty('database');
      expect(response.body).toHaveProperty('cache');
      expect(response.body).toHaveProperty('memory');
      expect(response.body).toHaveProperty('uptime');
      expect(['healthy', 'degraded', 'unhealthy']).toContain(response.body.status);
    });

    it('should deny system health for regular user', async () => {
      await AuthHelper.get(app, '/api/admin/system/health', regularUser)
        .expect(403);
    });
  });

  describe('GET /api/admin/system/metrics', () => {
    it('should return system metrics for admin user', async () => {
      const response = await AuthHelper.get(app, '/api/admin/system/metrics', adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('requests');
      expect(response.body).toHaveProperty('errors');
      expect(response.body).toHaveProperty('responseTime');
      expect(response.body).toHaveProperty('activeConnections');
      expect(response.body).toHaveProperty('timestamp');
    });

    it('should support time range filtering for metrics', async () => {
      const startTime = '2024-01-01T00:00:00Z';
      const endTime = '2024-12-31T23:59:59Z';
      
      const response = await AuthHelper.get(
        app, 
        `/api/admin/system/metrics?startTime=${startTime}&endTime=${endTime}`, 
        adminUser
      ).expect(200);

      expect(response.body).toHaveProperty('requests');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('POST /api/admin/maintenance/mode', () => {
    it('should enable maintenance mode for super admin', async () => {
      const response = await AuthHelper.post(app, '/api/admin/maintenance/mode', {
        enabled: true,
        message: 'System maintenance in progress'
      }, superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('maintenanceMode', true);
      expect(response.body).toHaveProperty('message', 'System maintenance in progress');
    });

    it('should disable maintenance mode for super admin', async () => {
      const response = await AuthHelper.post(app, '/api/admin/maintenance/mode', {
        enabled: false
      }, superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('maintenanceMode', false);
    });

    it('should deny maintenance mode toggle for regular admin', async () => {
      await AuthHelper.post(app, '/api/admin/maintenance/mode', {
        enabled: true
      }, adminUser)
        .expect(403);
    });
  });

  describe('GET /api/admin/logs/system', () => {
    it('should return system logs for admin user', async () => {
      const response = await AuthHelper.get(app, '/api/admin/logs/system', adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it('should support log level filtering', async () => {
      const response = await AuthHelper.get(app, '/api/admin/logs/system?level=error', adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it('should support pagination for system logs', async () => {
      const response = await AuthHelper.get(app, '/api/admin/logs/system?page=1&pageSize=10', adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('page', 1);
      expect(response.body.logs.length).toBeLessThanOrEqual(10);
    });
  });

  describe('Role-Based Access Control', () => {
    it('should enforce proper admin role requirements', async () => {
      // Regular user should be denied access to all admin endpoints
      await AuthHelper.get(app, '/api/admin/cache/stats', regularUser).expect(403);
      await AuthHelper.get(app, '/api/admin/system/health', regularUser).expect(403);
      await AuthHelper.get(app, '/api/admin/system/metrics', regularUser).expect(403);
      await AuthHelper.get(app, '/api/admin/logs/system', regularUser).expect(403);
    });

    it('should allow admin access to basic admin functions', async () => {
      // Admin user should have access to basic admin functions
      await AuthHelper.get(app, '/api/admin/cache/stats', adminUser).expect(200);
      await AuthHelper.get(app, '/api/admin/system/health', adminUser).expect(200);
      await AuthHelper.get(app, '/api/admin/system/metrics', adminUser).expect(200);
    });

    it('should allow super admin access to all admin functions', async () => {
      // Super admin should have access to all functions
      await AuthHelper.get(app, '/api/admin/cache/stats', superAdminUser).expect(200);
      await AuthHelper.get(app, '/api/admin/system/health', superAdminUser).expect(200);
      await AuthHelper.get(app, '/api/admin/system/metrics', superAdminUser).expect(200);
      await AuthHelper.post(app, '/api/admin/maintenance/mode', { enabled: false }, superAdminUser).expect(200);
    });
  });
});
