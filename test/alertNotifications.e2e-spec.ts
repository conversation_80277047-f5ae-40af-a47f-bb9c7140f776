import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { NotificationModule } from '../src/domains/alertNotifications/notification.module';
import { UtilsModule } from '../src/utils/UtilsModule';
import { CacheModule } from '@nestjs/cache-manager';

describe('AlertNotifications API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up AlertNotifications API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      NotificationModule,
      UtilsModule,
      CacheModule.register({
        isGlobal: true,
        store: 'memory',
        ttl: 300,
      }),
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ AlertNotifications API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down AlertNotifications API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/notifications', () => {
    it('should return notifications for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/notifications', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('notifications');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.notifications)).toBe(true);
    });

    it('should support pagination parameters', async () => {
      const response = await AuthHelper.get(app, '/api/notifications?page=1&pageSize=10', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('notifications');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.notifications.length).toBeLessThanOrEqual(10);
    });

    it('should support seen status filtering', async () => {
      const response = await AuthHelper.get(app, '/api/notifications?seen=false', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('notifications');
      expect(Array.isArray(response.body.notifications)).toBe(true);
    });

    it('should support alert zone filtering', async () => {
      const alertZoneId = seededData.alertZones[0]._id.toString();
      
      const response = await AuthHelper.get(app, `/api/notifications?alertZoneId=${alertZoneId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('notifications');
      expect(Array.isArray(response.body.notifications)).toBe(true);
    });

    it('should support date range filtering', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const response = await AuthHelper.get(
        app, 
        `/api/notifications?startDate=${startDate}&endDate=${endDate}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('notifications');
      expect(Array.isArray(response.body.notifications)).toBe(true);
    });
  });

  describe('GET /api/notifications/:id', () => {
    it('should return specific notification', async () => {
      // First get notifications to find a valid ID
      const notificationsResponse = await AuthHelper.get(app, '/api/notifications?pageSize=1', testUser);
      
      if (notificationsResponse.body.notifications.length > 0) {
        const notificationId = notificationsResponse.body.notifications[0]._id;
        
        const response = await AuthHelper.get(app, `/api/notifications/${notificationId}`, testUser)
          .expect(200);

        expect(response.body).toHaveProperty('_id', notificationId);
        expect(response.body).toHaveProperty('message');
        expect(response.body).toHaveProperty('timestamp');
      }
    });

    it('should return 404 for non-existent notification', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';
      
      await AuthHelper.get(app, `/api/notifications/${nonExistentId}`, testUser)
        .expect(404);
    });
  });

  describe('PATCH /api/notifications/:id/seen', () => {
    it('should mark notification as seen', async () => {
      // First create a notification or get an existing one
      const notificationsResponse = await AuthHelper.get(app, '/api/notifications?seen=false&pageSize=1', testUser);
      
      if (notificationsResponse.body.notifications.length > 0) {
        const notificationId = notificationsResponse.body.notifications[0]._id;
        
        const response = await AuthHelper.patch(app, `/api/notifications/${notificationId}/seen`, {}, testUser)
          .expect(200);

        expect(response.body).toHaveProperty('seen', true);
        expect(response.body).toHaveProperty('seenAt');
      }
    });

    it('should return 404 for non-existent notification', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';
      
      await AuthHelper.patch(app, `/api/notifications/${nonExistentId}/seen`, {}, testUser)
        .expect(404);
    });
  });

  describe('PATCH /api/notifications/mark-all-seen', () => {
    it('should mark all notifications as seen for user', async () => {
      const response = await AuthHelper.patch(app, '/api/notifications/mark-all-seen', {}, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('modifiedCount');
      expect(typeof response.body.modifiedCount).toBe('number');
    });
  });

  describe('POST /api/notifications', () => {
    const notificationData = {
      message: 'Test notification message',
      type: 'alert',
      alertZoneId: null, // Will be set in test
      priority: 'high',
      metadata: {
        source: 'test',
        details: 'Test notification details'
      }
    };

    it('should create notification for admin user', async () => {
      notificationData.alertZoneId = seededData.alertZones[0]._id.toString();
      
      const response = await AuthHelper.post(app, '/api/notifications', notificationData, adminUser)
        .expect(201);

      expect(response.body).toHaveProperty('message', notificationData.message);
      expect(response.body).toHaveProperty('type', notificationData.type);
      expect(response.body).toHaveProperty('priority', notificationData.priority);
      expect(response.body).toHaveProperty('org_id', adminUser.org_id);
      expect(response.body).toHaveProperty('seen', false);
    });

    it('should deny notification creation for regular user without permissions', async () => {
      await AuthHelper.post(app, '/api/notifications', notificationData, testUser)
        .expect(403);
    });
  });
});
