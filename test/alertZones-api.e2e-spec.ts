import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { AlertZoneModule } from '../src/domains/alertZones/alertZone.module';
import { AlertZoneController } from '../src/domains/alertZones/alertZone.controller';
import { AlertZoneService } from '../src/domains/alertZones/alertZone.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('AlertZones API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up AlertZones API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      AlertZoneModule,
      UtilsModule
    ], [
      AlertZoneController
    ], [
      AlertZoneService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ AlertZones API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down AlertZones API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/alertZones', () => {
    it('should return alert zones for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/alertZones', testUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // Should only return alert zones for user's organization
      response.body.forEach(zone => {
        expect(zone.orgId).toBe(testUser.org_id);
      });
    });

    it('should return different alert zones for different organizations', async () => {
      const org1Response = await AuthHelper.get(app, '/api/alertZones', testUser)
        .expect(200);

      const org2Response = await AuthHelper.get(app, '/api/alertZones', otherOrgUser)
        .expect(200);

      // Responses should be different (organization isolation)
      expect(org1Response.body).not.toEqual(org2Response.body);
    });
  });

  describe('POST /api/alertZones', () => {
    const alertZoneData = {
      name: 'Test Alert Zone',
      geometry: {
        type: 'Polygon',
        coordinates: [[[-74.0059, 40.7128], [-74.0059, 40.7228], [-73.9959, 40.7228], [-73.9959, 40.7128], [-74.0059, 40.7128]]]
      },
      latestStatus: 1
    };

    it('should create alert zone for authenticated user', async () => {
      const response = await AuthHelper.post(app, '/api/alertZones', alertZoneData, testUser)
        .expect(201);

      expect(response.body).toHaveProperty('name', alertZoneData.name);
      expect(response.body).toHaveProperty('orgId', testUser.org_id);
      expect(response.body).toHaveProperty('geometry');
    });

    it('should create alert zone with correct organization context', async () => {
      const response = await AuthHelper.post(app, '/api/alertZones', alertZoneData, otherOrgUser)
        .expect(201);

      expect(response.body).toHaveProperty('orgId', otherOrgUser.org_id);
    });
  });

  describe('GET /api/alertZones/:id', () => {
    it('should return specific alert zone for owner', async () => {
      // First create an alert zone
      const createResponse = await AuthHelper.post(app, '/api/alertZones', {
        name: 'Test Zone for Retrieval',
        geometry: {
          type: 'Polygon',
          coordinates: [[[-74.0059, 40.7128], [-74.0059, 40.7228], [-73.9959, 40.7228], [-73.9959, 40.7128], [-74.0059, 40.7128]]]
        }
      }, testUser);

      const zoneId = createResponse.body._id;

      // Then retrieve it
      const response = await AuthHelper.get(app, `/api/alertZones/${zoneId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('_id', zoneId);
      expect(response.body).toHaveProperty('name', 'Test Zone for Retrieval');
    });

    it('should not return alert zone from different organization', async () => {
      // Create alert zone with one user
      const createResponse = await AuthHelper.post(app, '/api/alertZones', {
        name: 'Private Zone',
        geometry: {
          type: 'Polygon',
          coordinates: [[[-74.0059, 40.7128], [-74.0059, 40.7228], [-73.9959, 40.7228], [-73.9959, 40.7128], [-74.0059, 40.7128]]]
        }
      }, testUser);

      const zoneId = createResponse.body._id;

      // Try to access with user from different organization
      await AuthHelper.get(app, `/api/alertZones/${zoneId}`, otherOrgUser)
        .expect(404); // Should not find it due to organization isolation
    });
  });
});
