import { MongoClient } from 'mongodb';
import { DockerManager } from './utils/docker-manager';

describe('AlertZones Database (e2e)', () => {
  let client: MongoClient;
  let db: any;

  beforeAll(async () => {
    console.log('🚀 Setting up AlertZones e2e tests...');

    // Start Docker containers
    await DockerManager.startContainers();
    await DockerManager.waitForHealthyContainers();

    // Connect directly to MongoDB
    const connectionString = '***********************************************************************';
    client = new MongoClient(connectionString);
    await client.connect();
    db = client.db('coddn_test');

    console.log('✅ AlertZones e2e test setup completed');
  }, 120000); // 2 minute timeout for setup

  afterAll(async () => {
    console.log('🧹 Tearing down AlertZones e2e tests...');

    if (client) {
      await client.close();
    }

    await DockerManager.stopContainers();
    await DockerManager.cleanupVolumes();
  }, 30000);

  beforeEach(async () => {
    // Clear collections before each test
    const collections = await db.listCollections().toArray();
    for (const collection of collections) {
      await db.collection(collection.name).deleteMany({});
    }
  });

  describe('Database Integration', () => {
    it('should connect to MongoDB and create collections', async () => {
      // Test database connection
      const adminDb = client.db('admin');
      const result = await adminDb.command({ ping: 1 });
      expect(result.ok).toBe(1);

      const collections = await db.listCollections().toArray();
      const collectionNames = collections.map((c: any) => c.name);

      // Check that our test collections exist or can be created
      expect(collectionNames.length).toBeGreaterThanOrEqual(0);
    });

    it('should create and manage test data', async () => {
      // Create test organization
      const testOrg = {
        name: 'Test Organization',
        auth0_id: 'test-auth0-id',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false
      };

      const orgResult = await db.collection('organizations').insertOne(testOrg);
      expect(orgResult.insertedId).toBeDefined();

      // Create test user
      const testUser = {
        name: 'Test User',
        email: '<EMAIL>',
        orgId: orgResult.insertedId,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false
      };

      const userResult = await db.collection('users').insertOne(testUser);
      expect(userResult.insertedId).toBeDefined();

      // Verify data exists
      const orgCount = await db.collection('organizations').countDocuments();
      const userCount = await db.collection('users').countDocuments();

      expect(orgCount).toBe(1);
      expect(userCount).toBe(1);
    });

    it('should handle MongoDB operations correctly', async () => {
      const testDoc = {
        name: 'Test Alert Zone',
        orgId: 'test-org-id',
        geometry: {
          type: 'Polygon',
          coordinates: [[[-74.0059, 40.7128], [-74.0059, 40.7228], [-73.9959, 40.7228], [-73.9959, 40.7128], [-74.0059, 40.7128]]]
        },
        latestStatus: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false
      };

      // Insert a document
      const result = await db.collection('alertzones').insertOne(testDoc);
      expect(result.insertedId).toBeDefined();

      // Find the document
      const found = await db.collection('alertzones').findOne({ _id: result.insertedId });
      expect(found).toBeDefined();
      expect(found.name).toBe(testDoc.name);

      // Update the document
      await db.collection('alertzones').updateOne(
        { _id: result.insertedId },
        { $set: { name: 'Updated Alert Zone' } }
      );

      // Verify update
      const updated = await db.collection('alertzones').findOne({ _id: result.insertedId });
      expect(updated.name).toBe('Updated Alert Zone');

      // Delete the document
      await db.collection('alertzones').deleteOne({ _id: result.insertedId });

      // Verify deletion
      const deleted = await db.collection('alertzones').findOne({ _id: result.insertedId });
      expect(deleted).toBeNull();
    });

    it('should support geospatial operations', async () => {
      // Create a geospatial index
      await db.collection('alertzones').createIndex({ geometry: '2dsphere' });

      const testAlertZone = {
        name: 'Geo Test Zone',
        geometry: {
          type: 'Polygon',
          coordinates: [[[-74.0059, 40.7128], [-74.0059, 40.7228], [-73.9959, 40.7228], [-73.9959, 40.7128], [-74.0059, 40.7128]]]
        },
        latestStatus: 1,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false
      };

      const result = await db.collection('alertzones').insertOne(testAlertZone);
      expect(result.insertedId).toBeDefined();

      // Test geospatial query
      const geoQuery = {
        geometry: {
          $geoIntersects: {
            $geometry: {
              type: 'Point',
              coordinates: [-74.0000, 40.7200]
            }
          }
        }
      };

      const geoResult = await db.collection('alertzones').findOne(geoQuery);
      expect(geoResult).toBeDefined();
      expect(geoResult.name).toBe('Geo Test Zone');
    });
  });
});
