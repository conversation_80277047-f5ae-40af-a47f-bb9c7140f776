import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { DashboardModule } from '../src/domains/dashboard/dashboard.module';
import { DashboardController } from '../src/domains/dashboard/dashboard.controller';
import { DashboardService } from '../src/domains/dashboard/dashboard.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('Dashboard API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Dashboard API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      DashboardModule,
      UtilsModule
    ], [
      DashboardController
    ], [
      DashboardService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ Dashboard API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Dashboard API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/dashboard/stats', () => {
    it('should return dashboard statistics for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/dashboard/stats', testUser)
        .expect(200);

      // Verify the response structure
      expect(response.body).toHaveProperty('totalEvents');
      expect(response.body).toHaveProperty('uniqueDrones');
      expect(response.body).toHaveProperty('averageDuration');
      expect(response.body).toHaveProperty('averageEventsPerDay');
      expect(response.body).toHaveProperty('groupedEvents');
      expect(response.body).toHaveProperty('groupBy');
      expect(response.body).toHaveProperty('dronesPerUasId');
      expect(response.body).toHaveProperty('eventsPerAlertZones');
      expect(response.body).toHaveProperty('eventsPerHour');

      // Verify data types
      expect(typeof response.body.totalEvents).toBe('number');
      expect(typeof response.body.uniqueDrones).toBe('number');
      expect(Array.isArray(response.body.groupedEvents)).toBe(true);
      expect(Array.isArray(response.body.dronesPerUasId)).toBe(true);
      expect(Array.isArray(response.body.eventsPerAlertZones)).toBe(true);
      expect(Array.isArray(response.body.eventsPerHour)).toBe(true);
    });

    it('should support date range filtering', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const response = await AuthHelper.get(
        app, 
        `/api/dashboard/stats?startDate=${startDate}&endDate=${endDate}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('totalEvents');
      expect(response.body).toHaveProperty('groupBy', 'daily'); // default groupBy
    });

    it('should support alert zone filtering', async () => {
      const alertZoneIds = seededData.alertZones.map(zone => zone._id.toString()).join(',');
      
      const response = await AuthHelper.get(
        app, 
        `/api/dashboard/stats?alertZoneIds=${alertZoneIds}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('totalEvents');
      expect(response.body).toHaveProperty('eventsPerAlertZones');
    });

    it('should support UAS ID filtering', async () => {
      const uasIds = 'UAS001,UAS002,UAS003';
      
      const response = await AuthHelper.get(
        app, 
        `/api/dashboard/stats?uasIds=${uasIds}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('totalEvents');
      expect(response.body).toHaveProperty('dronesPerUasId');
    });

    it('should support different groupBy parameters', async () => {
      const groupByOptions = ['daily', 'weekly', 'monthly', 'yearly'];
      
      for (const groupBy of groupByOptions) {
        const response = await AuthHelper.get(
          app, 
          `/api/dashboard/stats?groupBy=${groupBy}`, 
          testUser
        ).expect(200);

        expect(response.body).toHaveProperty('groupBy', groupBy);
        expect(response.body).toHaveProperty('groupedEvents');
      }
    });

    it('should reject invalid groupBy parameter', async () => {
      await AuthHelper.get(app, '/api/dashboard/stats?groupBy=invalid', testUser)
        .expect(400);
    });

    it('should return different stats for different organizations', async () => {
      const org1Response = await AuthHelper.get(app, '/api/dashboard/stats', testUser)
        .expect(200);

      const org2Response = await AuthHelper.get(app, '/api/dashboard/stats', otherOrgUser)
        .expect(200);

      // Both should have valid structure but potentially different data
      expect(org1Response.body).toHaveProperty('totalEvents');
      expect(org2Response.body).toHaveProperty('totalEvents');
      
      // The responses should be scoped to their respective organizations
      expect(typeof org1Response.body.totalEvents).toBe('number');
      expect(typeof org2Response.body.totalEvents).toBe('number');
    });

    it('should support complex filtering combinations', async () => {
      const alertZoneIds = seededData.alertZones.slice(0, 2).map(zone => zone._id.toString()).join(',');
      const uasIds = 'UAS001,UAS002';
      const startDate = '2024-01-01';
      const endDate = '2024-06-30';
      const groupBy = 'weekly';
      
      const response = await AuthHelper.get(
        app, 
        `/api/dashboard/stats?alertZoneIds=${alertZoneIds}&uasIds=${uasIds}&startDate=${startDate}&endDate=${endDate}&groupBy=${groupBy}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('totalEvents');
      expect(response.body).toHaveProperty('groupBy', groupBy);
      expect(response.body).toHaveProperty('groupedEvents');
      expect(response.body).toHaveProperty('eventsPerAlertZones');
      expect(response.body).toHaveProperty('dronesPerUasId');
    });
  });

  describe('Organization Isolation', () => {
    it('should only return stats for user organization', async () => {
      const response = await AuthHelper.get(app, '/api/dashboard/stats', testUser)
        .expect(200);

      // The response should be scoped to the user's organization
      // This is enforced by the orgId parameter in the service
      expect(response.body).toHaveProperty('totalEvents');
      expect(typeof response.body.totalEvents).toBe('number');
    });
  });
});
