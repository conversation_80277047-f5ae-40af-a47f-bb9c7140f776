import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule } from '@nestjs/config';
import Constants from 'src/common/constants';
import NotificationSchema from 'src/domains/alertNotifications/notification.schema';
import AlertZoneSchema from 'src/domains/alertZones/alertZone.schema';

// All schemas are now handled by their respective domain modules

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    // Main database connection for testing
    MongooseModule.forRoot(
      process.env.TEST_MONGODB_CONNECTION_STRING || '***********************************************************************',
      {
        dbName: process.env.TEST_DATABASE_NAME || 'coddn_test',
        connectionName: 'default',
      }
    ),
    MongooseModule.forFeature([
      { name: Constants.notification, schema: NotificationSchema },
      { name: Constants.alertZones, schema: AlertZoneSchema },
    ]),
    
    // All schemas are now handled by their respective domain modules
    // No need to register any schemas here to avoid conflicts
  ],
  exports: [MongooseModule],
})
export class TestDatabaseModule {}
