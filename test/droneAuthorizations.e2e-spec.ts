import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { DroneAuthorizationModule } from '../src/domains/droneAuthorizations/droneAuthorization.module';
import { DroneAuthorizationController } from '../src/domains/droneAuthorizations/droneAuthorization.controller';
import { DroneAuthorizationService } from '../src/domains/droneAuthorizations/droneAuthorization.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('DroneAuthorizations API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up DroneAuthorizations API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      DroneAuthorizationModule,
      UtilsModule
    ], [
      DroneAuthorizationController
    ], [
      DroneAuthorizationService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ DroneAuthorizations API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down DroneAuthorizations API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/drone-authorizations', () => {
    it('should return drone authorizations for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/drone-authorizations', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('authorizations');
      expect(response.body).toHaveProperty('total');
      expect(Array.isArray(response.body.authorizations)).toBe(true);
    });

    it('should support pagination parameters', async () => {
      const response = await AuthHelper.get(app, '/api/drone-authorizations?skip=0&limit=5', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('authorizations');
      expect(response.body).toHaveProperty('total');
    });
  });

  describe('POST /api/drone-authorizations', () => {
    const authorizationData = {
      drone_id: 'test-drone-id',
      authorization_type: 'temporary',
      valid_from: new Date(),
      valid_until: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      notes: 'Test authorization'
    };

    it('should create drone authorization for authenticated user', async () => {
      const response = await AuthHelper.post(app, '/api/drone-authorizations', authorizationData, testUser)
        .expect(201);

      expect(response.body).toHaveProperty('drone_id', authorizationData.drone_id);
      expect(response.body).toHaveProperty('authorization_type', authorizationData.authorization_type);
      expect(response.body).toHaveProperty('authorized_by');
      expect(response.body).toHaveProperty('org_id', testUser.org_id);
    });

    it('should set authorized_by from user context', async () => {
      const response = await AuthHelper.post(app, '/api/drone-authorizations', authorizationData, adminUser)
        .expect(201);

      expect(response.body).toHaveProperty('authorized_by');
      expect(response.body.authorized_by).toBeTruthy();
    });

    it('should create authorization with correct organization context', async () => {
      const response = await AuthHelper.post(app, '/api/drone-authorizations', authorizationData, otherOrgUser)
        .expect(201);

      expect(response.body).toHaveProperty('org_id', otherOrgUser.org_id);
    });
  });

  describe('GET /api/drone-authorizations/:id', () => {
    it('should return specific drone authorization', async () => {
      // First create an authorization
      const createResponse = await AuthHelper.post(app, '/api/drone-authorizations', {
        drone_id: 'test-drone-for-retrieval',
        authorization_type: 'permanent',
        valid_from: new Date(),
        notes: 'Test authorization for retrieval'
      }, testUser);

      const authId = createResponse.body._id;

      // Then retrieve it
      const response = await AuthHelper.get(app, `/api/drone-authorizations/${authId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('_id', authId);
      expect(response.body).toHaveProperty('drone_id', 'test-drone-for-retrieval');
    });
  });

  describe('GET /api/drone-authorizations/drone/:droneId', () => {
    it('should return authorizations for specific drone', async () => {
      const droneId = 'test-drone-multiple-auths';
      
      // Create multiple authorizations for the same drone
      await AuthHelper.post(app, '/api/drone-authorizations', {
        drone_id: droneId,
        authorization_type: 'temporary',
        valid_from: new Date(),
        notes: 'First authorization'
      }, testUser);

      await AuthHelper.post(app, '/api/drone-authorizations', {
        drone_id: droneId,
        authorization_type: 'permanent',
        valid_from: new Date(),
        notes: 'Second authorization'
      }, testUser);

      const response = await AuthHelper.get(app, `/api/drone-authorizations/drone/${droneId}`, testUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThanOrEqual(2);
      response.body.forEach(auth => {
        expect(auth.drone_id).toBe(droneId);
      });
    });
  });

  describe('PUT /api/drone-authorizations/:id', () => {
    it('should update drone authorization', async () => {
      // Create an authorization first
      const createResponse = await AuthHelper.post(app, '/api/drone-authorizations', {
        drone_id: 'test-drone-update',
        authorization_type: 'temporary',
        valid_from: new Date(),
        notes: 'Original notes'
      }, testUser);

      const authId = createResponse.body._id;

      // Update the authorization
      const updateData = {
        authorization_type: 'permanent',
        notes: 'Updated notes'
      };

      const response = await AuthHelper.put(app, `/api/drone-authorizations/${authId}`, updateData, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('authorization_type', 'permanent');
      expect(response.body).toHaveProperty('notes', 'Updated notes');
    });
  });

  describe('DELETE /api/drone-authorizations/:id', () => {
    it('should delete drone authorization', async () => {
      // Create an authorization first
      const createResponse = await AuthHelper.post(app, '/api/drone-authorizations', {
        drone_id: 'test-drone-delete',
        authorization_type: 'temporary',
        valid_from: new Date(),
        notes: 'To be deleted'
      }, testUser);

      const authId = createResponse.body._id;

      // Delete the authorization
      await AuthHelper.delete(app, `/api/drone-authorizations/${authId}`, testUser)
        .expect(200);

      // Verify it's deleted
      await AuthHelper.get(app, `/api/drone-authorizations/${authId}`, testUser)
        .expect(404);
    });
  });

  describe('Organization Isolation', () => {
    it('should not access authorizations from different organization', async () => {
      // Create authorization with one user
      const createResponse = await AuthHelper.post(app, '/api/drone-authorizations', {
        drone_id: 'private-drone-auth',
        authorization_type: 'permanent',
        valid_from: new Date(),
        notes: 'Private authorization'
      }, testUser);

      const authId = createResponse.body._id;

      // Try to access with user from different organization
      await AuthHelper.get(app, `/api/drone-authorizations/${authId}`, otherOrgUser)
        .expect(404);
    });
  });
});
