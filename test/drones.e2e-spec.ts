import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { DroneModule } from '../src/domains/drones/drone.module';
import { DroneController } from '../src/domains/drones/drone.controller';
import { DroneService } from '../src/domains/drones/drone.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('Drones API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Drones API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      DroneModule,
      UtilsModule
    ], [
      DroneController
    ], [
      DroneService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ Drones API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Drones API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/drones', () => {
    it('should return drones for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/drones', testUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // Should only return drones for user's organization
      response.body.forEach(drone => {
        expect(drone.orgId).toBe(testUser.org_id);
      });
    });

    it('should return different drones for different organizations', async () => {
      const org1Response = await AuthHelper.get(app, '/api/drones', testUser)
        .expect(200);

      const org2Response = await AuthHelper.get(app, '/api/drones', otherOrgUser)
        .expect(200);

      // Responses should be different (organization isolation)
      expect(org1Response.body).not.toEqual(org2Response.body);
    });
  });

  describe('POST /api/drones', () => {
    const droneData = {
      name: 'Test Drone',
      serialNumber: 'TD-001',
      model: 'TestModel X1',
      manufacturer: 'TestCorp',
      location: {
        lat: 40.7128,
        lon: -74.0060
      }
    };

    it('should create drone for authenticated user', async () => {
      const response = await AuthHelper.post(app, '/api/drones', droneData, testUser)
        .expect(201);

      expect(response.body).toHaveProperty('name', droneData.name);
      expect(response.body).toHaveProperty('serialNumber', droneData.serialNumber);
      expect(response.body).toHaveProperty('orgId', testUser.org_id);
    });

    it('should create drone with correct organization context', async () => {
      const response = await AuthHelper.post(app, '/api/drones', droneData, otherOrgUser)
        .expect(201);

      expect(response.body).toHaveProperty('orgId', otherOrgUser.org_id);
    });

    it('should validate required fields', async () => {
      const invalidDroneData = {
        name: 'Test Drone'
        // Missing required fields
      };

      await AuthHelper.post(app, '/api/drones', invalidDroneData, testUser)
        .expect(400);
    });
  });

  describe('GET /api/drones/:id', () => {
    it('should return specific drone for owner', async () => {
      // First create a drone
      const createResponse = await AuthHelper.post(app, '/api/drones', {
        name: 'Test Drone for Retrieval',
        serialNumber: 'TD-RETRIEVE-001',
        model: 'TestModel',
        manufacturer: 'TestCorp'
      }, testUser);

      const droneId = createResponse.body._id;

      // Then retrieve it
      const response = await AuthHelper.get(app, `/api/drones/${droneId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('_id', droneId);
      expect(response.body).toHaveProperty('name', 'Test Drone for Retrieval');
    });

    it('should not return drone from different organization', async () => {
      // Create drone with one user
      const createResponse = await AuthHelper.post(app, '/api/drones', {
        name: 'Private Drone',
        serialNumber: 'PD-001',
        model: 'PrivateModel',
        manufacturer: 'PrivateCorp'
      }, testUser);

      const droneId = createResponse.body._id;

      // Try to access with user from different organization
      await AuthHelper.get(app, `/api/drones/${droneId}`, otherOrgUser)
        .expect(404); // Should not find it due to organization isolation
    });
  });

  describe('PUT /api/drones/:id', () => {
    it('should update drone for owner', async () => {
      // Create a drone first
      const createResponse = await AuthHelper.post(app, '/api/drones', {
        name: 'Original Drone',
        serialNumber: 'OD-001',
        model: 'OriginalModel',
        manufacturer: 'OriginalCorp'
      }, testUser);

      const droneId = createResponse.body._id;

      // Update the drone
      const updateData = {
        name: 'Updated Drone',
        model: 'UpdatedModel'
      };

      const response = await AuthHelper.put(app, `/api/drones/${droneId}`, updateData, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('name', 'Updated Drone');
      expect(response.body).toHaveProperty('model', 'UpdatedModel');
    });

    it('should not update drone from different organization', async () => {
      // Create drone with one user
      const createResponse = await AuthHelper.post(app, '/api/drones', {
        name: 'Private Drone',
        serialNumber: 'PD-002',
        model: 'PrivateModel',
        manufacturer: 'PrivateCorp'
      }, testUser);

      const droneId = createResponse.body._id;

      // Try to update with user from different organization
      await AuthHelper.put(app, `/api/drones/${droneId}`, { name: 'Hacked Drone' }, otherOrgUser)
        .expect(404);
    });
  });
});
