import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { Auth<PERSON>elper, TestUser } from './utils/auth-helper';
import { EventModule } from '../src/domains/events/event.module';
import { EventController } from '../src/domains/events/event.controller';
import { EventService } from '../src/domains/events/event.service';
import { EventProfileService } from '../src/domains/events/eventProfile.service';
import { EventHistoryService } from '../src/domains/events/eventHistory.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('Events API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Events API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      EventModule,
      UtilsModule
    ], [
      EventController
    ], [
      EventService,
      EventProfileService,
      EventHistoryService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ Events API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Events API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/events', () => {
    it('should return events for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/events', testUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should support pagination parameters', async () => {
      const response = await AuthHelper.get(app, '/api/events?page=1&limit=10', testUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should support date filtering', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const response = await AuthHelper.get(
        app, 
        `/api/events?startDate=${startDate}&endDate=${endDate}`, 
        testUser
      ).expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('GET /api/events/profile/:eventId', () => {
    it('should return event profile for specific event', async () => {
      const eventId = 'test-event-profile-001';
      
      const response = await AuthHelper.get(app, `/api/events/profile/${eventId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('EVENT_ID', eventId);
    });

    it('should handle non-existent event profile', async () => {
      const nonExistentEventId = 'non-existent-event-123';
      
      await AuthHelper.get(app, `/api/events/profile/${nonExistentEventId}`, testUser)
        .expect(404);
    });
  });

  describe('GET /api/events/history/:eventId', () => {
    it('should return event history for specific event', async () => {
      const eventId = 'test-event-history-001';
      
      const response = await AuthHelper.get(app, `/api/events/history/${eventId}`, testUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should support limit parameter for event history', async () => {
      const eventId = 'test-event-history-001';
      const limit = 25;
      
      const response = await AuthHelper.get(app, `/api/events/history/${eventId}?limit=${limit}`, testUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(limit);
    });
  });

  describe('GET /api/events/device/:deviceId', () => {
    it('should return events for specific device', async () => {
      const deviceId = 'test-device-001';
      
      const response = await AuthHelper.get(app, `/api/events/device/${deviceId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('profiles');
      expect(response.body).toHaveProperty('history');
      expect(Array.isArray(response.body.profiles)).toBe(true);
      expect(Array.isArray(response.body.history)).toBe(true);
    });
  });

  describe('GET /api/events/latest/:eventId', () => {
    it('should return latest event history for specific event', async () => {
      const eventId = 'test-event-latest-001';
      
      const response = await AuthHelper.get(app, `/api/events/latest/${eventId}`, testUser)
        .expect(200);

      // Response can be null if no history exists, or an object if it does
      if (response.body !== null) {
        expect(response.body).toHaveProperty('EVENT_ID', eventId);
      }
    });
  });

  describe('GET /api/events/stats/overview', () => {
    it('should return event statistics overview', async () => {
      const response = await AuthHelper.get(app, '/api/events/stats/overview', testUser)
        .expect(200);

      expect(response.body).toBeDefined();
      // The exact structure depends on the implementation
    });

    it('should support filtering in statistics', async () => {
      const response = await AuthHelper.get(
        app, 
        '/api/events/stats/overview?startDate=2024-01-01&endDate=2024-12-31', 
        testUser
      ).expect(200);

      expect(response.body).toBeDefined();
    });
  });

  describe('GET /api/events/stats/profiles', () => {
    it('should return event profile statistics', async () => {
      const response = await AuthHelper.get(app, '/api/events/stats/profiles', testUser)
        .expect(200);

      expect(response.body).toBeDefined();
    });
  });

  describe('GET /api/events/stats/history', () => {
    it('should return event history statistics', async () => {
      const response = await AuthHelper.get(app, '/api/events/stats/history', testUser)
        .expect(200);

      expect(response.body).toBeDefined();
    });

    it('should support eventId filtering in history stats', async () => {
      const eventId = 'test-event-001';

      const response = await AuthHelper.get(app, `/api/events/stats/history?eventId=${eventId}`, testUser)
        .expect(200);

      expect(response.body).toBeDefined();
    });

    it('should support deviceId filtering in history stats', async () => {
      const deviceId = 'test-device-001';

      const response = await AuthHelper.get(app, `/api/events/stats/history?deviceId=${deviceId}`, testUser)
        .expect(200);

      expect(response.body).toBeDefined();
    });
  });

  describe('Event Filtering and Search', () => {
    it('should support complex event filtering', async () => {
      const response = await AuthHelper.get(
        app,
        '/api/events?page=1&limit=20&startDate=2024-01-01&endDate=2024-12-31&deviceId=test-device',
        testUser
      ).expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should handle invalid date ranges gracefully', async () => {
      const response = await AuthHelper.get(
        app,
        '/api/events?startDate=invalid-date&endDate=2024-12-31',
        testUser
      ).expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('Organization Isolation', () => {
    it('should return different events for different organizations', async () => {
      const org1Response = await AuthHelper.get(app, '/api/events', testUser)
        .expect(200);

      const org2Response = await AuthHelper.get(app, '/api/events', otherOrgUser)
        .expect(200);

      // Both should return arrays but potentially different data
      expect(Array.isArray(org1Response.body)).toBe(true);
      expect(Array.isArray(org2Response.body)).toBe(true);
    });
  });
});
