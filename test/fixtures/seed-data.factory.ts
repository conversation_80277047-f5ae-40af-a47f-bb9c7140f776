import { Types } from 'mongoose';
import { AlertZoneStatusEnum } from 'src/common/enums/AlertZoneStatusEnum';

export class SeedDataFactory {
  static createOrganization(overrides: Partial<any> = {}) {
    return {
      _id: new Types.ObjectId(),
      auth0_id: `org_${Math.random().toString(36).substr(2, 9)}`,
      display_name: 'Test Organization',
      name: 'test-org',
      branding: {
        logo: 'https://example.com/logo.png',
        primaryColor: '#007bff',
        secondaryColor: '#6c757d'
      },
      service_zones: [],
      ...overrides
    };
  }

  static createUser(orgId: Types.ObjectId, overrides: Partial<any> = {}) {
    const userId = Math.random().toString(36).substr(2, 9);
    return {
      sub: `auth0|${userId}`,
      given_name: 'Test',
      family_name: 'User',
      nickname: 'testuser',
      name: 'Test User',
      picture: 'https://example.com/avatar.png',
      updated_at: new Date().toISOString(),
      created_at: new Date().toISOString(),
      email: `test.user.${userId}@example.com`,
      email_verified: true,
      phone_number: '+1234567890',
      phone_verified: true,
      service_zones: [],
      org_id: orgId.toString(),
      org_name: 'Test Organization',
      orgs: [{ id: orgId.toString(), name: 'Test Organization' }],
      roles: ['user'],
      last_activity: new Date().toISOString(),
      ...overrides
    };
  }

  static createServiceZone(overrides: Partial<any> = {}) {
    return {
      _id: new Types.ObjectId(),
      name: `Test Service Zone ${Math.random().toString(36).substr(2, 5)}`,
      h3_indexes: ['8a2a1072b59ffff', '8a2a1072b5bffff', '8a2a1072b5dffff'],
      url: 'https://example.com/service-zone',
      ...overrides
    };
  }

  static createAlertZone(orgId: Types.ObjectId, serviceZoneIds: Types.ObjectId[] = [], overrides: Partial<any> = {}) {
    return {
      _id: new Types.ObjectId(),
      name: `Test Alert Zone ${Math.random().toString(36).substr(2, 5)}`,
      orgId: orgId,
      serviceZones: serviceZoneIds,
      geometry: {
        type: 'Polygon',
        coordinates: [[
          [-74.0059, 40.7128], // New York coordinates
          [-74.0059, 40.7228],
          [-73.9959, 40.7228],
          [-73.9959, 40.7128],
          [-74.0059, 40.7128]
        ]]
      },
      latestStatus: AlertZoneStatusEnum.AVAILABLE,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false,
      deletedAt: null,
      ...overrides
    };
  }

  static createDrone(orgId: string, overrides: Partial<any> = {}) {
    const deviceId = `DRONE_${Math.random().toString(36).substr(2, 8).toUpperCase()}`;
    return {
      _id: new Types.ObjectId(),
      device_id: deviceId,
      org_id: orgId,
      display_name: `Test Drone ${deviceId}`,
      tag: 'test-drone',
      uas_id: `UAS_${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
      operator_id: `OP_${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false,
      deletedAt: null,
      ...overrides
    };
  }

  static createDroneAuthorization(droneId: Types.ObjectId, orgId: string, alertZoneIds: Types.ObjectId[] = [], overrides: Partial<any> = {}) {
    return {
      _id: new Types.ObjectId(),
      drone_id: droneId,
      org_id: orgId,
      is_authorized: true,
      alert_zones: alertZoneIds,
      authorize_expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      authorized_by: '<EMAIL>',
      notes: 'Test authorization for e2e testing',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false,
      deletedAt: null,
      ...overrides
    };
  }

  static createSensor(overrides: Partial<any> = {}) {
    const nodeId = `SENSOR_${Math.random().toString(36).substr(2, 8).toUpperCase()}`;
    return {
      _id: new Types.ObjectId(),
      NODE_ID: nodeId,
      HOST_NAME: `sensor-host-${nodeId.toLowerCase()}`,
      TIME_STAMP: new Date(),
      assignment: {
        ASSIGNMENT_ID: `ASSIGN_${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
        ASSIGNMENT_NAME: 'Test Assignment',
        ASSIGNMENT_TYPE: 'monitoring'
      },
      connectivity: {
        CONNECTIVITY_STATUS: 'connected',
        LAST_SEEN: new Date(),
        SIGNAL_STRENGTH: -45
      },
      first_seen: new Date(),
      location: {
        LAT: 40.7128,
        LON: -74.0060,
        ALTITUDE: 10.5,
        ACCURACY: 5.0
      },
      registration: {
        REGISTRATION_ID: `REG_${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
        REGISTRATION_STATUS: 'active'
      },
      runtime: {
        UPTIME: 86400,
        CPU_USAGE: 25.5,
        MEMORY_USAGE: 512
      },
      telemetry: {
        TEMPERATURE: 22.5,
        HUMIDITY: 45.0,
        BATTERY_LEVEL: 85
      },
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false,
      deletedAt: null,
      ...overrides
    };
  }

  static createEventProfile(overrides: Partial<any> = {}) {
    const eventId = `EVENT_${Math.random().toString(36).substr(2, 8).toUpperCase()}`;
    const deviceId = `DEVICE_${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    return {
      _id: new Types.ObjectId(),
      INCIDENT_ID: `INC_${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
      EVENT_ID: eventId,
      ALERT: 1,
      ALERT_GPS: [40.7128, -74.0060],
      ALERT_REFERENCE: 'Test Alert Reference',
      ALERT_TIME: new Date(),
      COMPLETE: 0,
      CURSOR_LAT: 40.7128,
      CURSOR_LON: -74.0060,
      DEPLOYMENT_ID: `DEP_${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
      DEPLOYMENT_NAME: 'Test Deployment',
      DEVICE_ID: deviceId,
      DEVICE_NAME: `Test Device ${deviceId}`,
      DEVICE_TYPE: 1,
      DURATION: 300,
      END_LAT: 40.7138,
      END_LON: -74.0050,
      END_REFERENCE: 'Test End Reference',
      END_TIME: new Date(Date.now() + 300000),
      FIRST_DETECTION: new Date(),
      GPS: [40.7128, -74.0060],
      INFO: {
        frame_time: new Date().toISOString(),
        wlan_radio_channel: '6',
        wlan_radio_frequency: '2437',
        wlan_radio_signal_dbm: '-45',
        wlan_bssid: '00:11:22:33:44:55',
        wlan_ssid: 'TEST_DRONE_SSID',
        opendroneid: 'test-drone-id',
        OpenDroneID_basicID_id_asc: 'TEST123456789',
        OpenDroneID_basicID_uaType: '1',
        OpenDroneID_loc_status: '1',
        OpenDroneID_loc_direction: '90',
        OpenDroneID_loc_speed: 15.5,
        OpenDroneID_loc_vspeed: '2.5',
        OpenDroneID_loc_lat: 40.7128,
        OpenDroneID_loc_lon: -74.0060,
        OpenDroneID_loc_geoAlt: '100',
        OpenDroneID_loc_height: '50',
        OpenDroneID_operator_id: 'TEST_OPERATOR_123',
        OpenDroneID_operator_type: '1',
        OpenDroneID_system_lat: 40.7128,
        OpenDroneID_system_lon: -74.0060
      },
      LAST_DETECTION: new Date(),
      MANUFACTURER: 'Test Manufacturer',
      MODE: 1,
      PROCESSED_ITEMS: [],
      REDUNDENCY: 'primary',
      REFERENCE: 'Test Reference',
      START_LAT: 40.7128,
      START_LON: -74.0060,
      START_REFERENCE: 'Test Start Reference',
      START_TIME: new Date(),
      STATS: {
        DISTANCE: '1.5km',
        VELOCITY: 15.5,
        DIRECTION: 'NE',
        TOA: '300s',
        BEARING: '45°',
        ALTITUDE: '100m'
      },
      STATUS: 1,
      TIME_STAMP: new Date(),
      TRACK_MODE: 1,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      isDeleted: false,
      deletedAt: null,
      ...overrides
    };
  }
}
