{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "testTimeout": 120000, "globalSetup": "<rootDir>/jest.global-setup.ts", "globalTeardown": "<rootDir>/jest.global-teardown.ts", "setupFilesAfterEnv": ["<rootDir>/jest.setup.e2e.ts"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/../src/$1"}, "collectCoverageFrom": ["../src/**/*.(t|j)s"], "coverageDirectory": "../coverage-e2e", "maxWorkers": 1, "forceExit": true, "detectOpenHandles": true, "verbose": true}