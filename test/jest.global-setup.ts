import { DockerManager } from './utils/docker-manager';

export default async function globalSetup() {
  console.log('🚀 Starting global e2e test setup...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.ENABLE_CACHE = 'false';
  process.env.TEST_MONGODB_CONNECTION_STRING = '***********************************************************************';
  process.env.TEST_DATABASE_NAME = 'coddn_test';
  process.env.TEST_SPECTRUM_MONGODB_CONNECTION_STRING = '**************************************************************************';
  process.env.TEST_SPECTRUM_DATABASE_NAME = 'spectrum_test';

  try {
    // Start Docker containers once for all tests
    await DockerManager.startContainers();
    console.log('✅ Global e2e test setup completed');
  } catch (error) {
    console.error('❌ Failed to start Docker containers:', error);
    throw error;
  }
}
