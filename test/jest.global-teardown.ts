import { DockerManager } from './utils/docker-manager';

export default async function globalTeardown() {
  console.log('🧹 Starting global e2e test teardown...');
  
  try {
    // Stop Docker containers after all tests
    await DockerManager.stopContainers();
    await DockerManager.cleanupVolumes();
    console.log('✅ Global e2e test teardown completed');
  } catch (error) {
    console.error('❌ Failed to stop Docker containers:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
}
