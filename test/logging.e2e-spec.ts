import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { LoggingModule } from '../src/domains/logging/logging.module';
import { LoggingController } from '../src/domains/logging/logging.controller';
import { LoggingService } from '../src/domains/logging/logging.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('Logging API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Logging API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      LoggingModule,
      UtilsModule
    ], [
      LoggingController
    ], [
      LoggingService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString(),
      permissions: ['read:logs']
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      permissions: ['read:logs', 'admin:logs']
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString(),
      permissions: ['read:logs']
    });

    console.log('✅ Logging API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Logging API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/logs', () => {
    it('should return logs for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/logs', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it('should support pagination parameters', async () => {
      const response = await AuthHelper.get(app, '/api/logs?page=1&pageSize=10', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('page', 1);
      expect(response.body.logs.length).toBeLessThanOrEqual(10);
    });

    it('should support entity filtering', async () => {
      const response = await AuthHelper.get(app, '/api/logs?entity=ALERT_ZONE', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it('should support action filtering', async () => {
      const response = await AuthHelper.get(app, '/api/logs?action=CREATE', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it('should support date range filtering', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const response = await AuthHelper.get(
        app, 
        `/api/logs?startDate=${startDate}&endDate=${endDate}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it('should support user filtering', async () => {
      const userId = testUser.sub;
      
      const response = await AuthHelper.get(app, `/api/logs?userId=${userId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it('should support entity ID filtering', async () => {
      const entityId = seededData.alertZones[0]._id.toString();
      
      const response = await AuthHelper.get(app, `/api/logs?entityId=${entityId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(Array.isArray(response.body.logs)).toBe(true);
    });

    it('should support combined filtering', async () => {
      const response = await AuthHelper.get(
        app, 
        '/api/logs?entity=ALERT_ZONE&action=CREATE&page=1&pageSize=5', 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('logs');
      expect(response.body).toHaveProperty('page', 1);
      expect(response.body.logs.length).toBeLessThanOrEqual(5);
    });
  });

  describe('GET /api/logs/:id', () => {
    it('should return specific log entry', async () => {
      // First get logs to find a valid ID
      const logsResponse = await AuthHelper.get(app, '/api/logs?pageSize=1', testUser);
      
      if (logsResponse.body.logs.length > 0) {
        const logId = logsResponse.body.logs[0]._id;
        
        const response = await AuthHelper.get(app, `/api/logs/${logId}`, testUser)
          .expect(200);

        expect(response.body).toHaveProperty('_id', logId);
        expect(response.body).toHaveProperty('entity');
        expect(response.body).toHaveProperty('action');
        expect(response.body).toHaveProperty('timestamp');
      }
    });

    it('should return 404 for non-existent log', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';
      
      await AuthHelper.get(app, `/api/logs/${nonExistentId}`, testUser)
        .expect(404);
    });
  });

  describe('POST /api/logs', () => {
    const logData = {
      entity: 'ALERT_ZONE',
      action: 'CREATE',
      entityId: 'test-entity-id',
      details: {
        name: 'Test Alert Zone',
        description: 'Created for testing'
      }
    };

    it('should create log entry for admin user', async () => {
      const response = await AuthHelper.post(app, '/api/logs', logData, adminUser)
        .expect(201);

      expect(response.body).toHaveProperty('entity', logData.entity);
      expect(response.body).toHaveProperty('action', logData.action);
      expect(response.body).toHaveProperty('entityId', logData.entityId);
      expect(response.body).toHaveProperty('userId');
      expect(response.body).toHaveProperty('org_id', adminUser.org_id);
      expect(response.body).toHaveProperty('timestamp');
    });

    it('should deny log creation for regular user without admin permissions', async () => {
      await AuthHelper.post(app, '/api/logs', logData, testUser)
        .expect(403);
    });
  });
});
