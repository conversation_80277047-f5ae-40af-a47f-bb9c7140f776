import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { OrganizationModule } from '../src/domains/organizations/organization.module';
import { OrganizationController } from '../src/domains/organizations/organization.controller';
import { OrganizationService } from '../src/domains/organizations/organization.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('Organizations API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let regularUser: TestUser;
  let adminUser: TestUser;
  let superAdminUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Organizations API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      OrganizationModule,
      UtilsModule
    ], [
      OrganizationController
    ], [
      OrganizationService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users with different role levels
    regularUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['user']
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['admin']
    });

    // Super admin can see all organizations
    superAdminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['ad-admin', 'super-admin'],
      permissions: ['admin:organizations', 'read:all-organizations']
    });

    console.log('✅ Organizations API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Organizations API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/organizations', () => {
    it('should return organizations for regular user (own org only)', async () => {
      const response = await AuthHelper.get(app, '/api/organizations', regularUser)
        .expect(200);

      expect(response.body).toHaveProperty('organizations');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.organizations)).toBe(true);
      
      // Regular user should only see their own organization
      response.body.organizations.forEach(org => {
        expect(org.auth0_id).toBe(regularUser.org_id);
      });
    });

    it('should return all organizations for super admin', async () => {
      const response = await AuthHelper.get(app, '/api/organizations', superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('organizations');
      expect(Array.isArray(response.body.organizations)).toBe(true);
      
      // Super admin should see multiple organizations
      expect(response.body.organizations.length).toBeGreaterThan(1);
    });

    it('should support pagination parameters', async () => {
      const response = await AuthHelper.get(app, '/api/organizations?page=1&pageSize=5', superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('page', 1);
      expect(response.body).toHaveProperty('totalPages');
      expect(response.body.organizations.length).toBeLessThanOrEqual(5);
    });

    it('should support filtering parameters', async () => {
      const response = await AuthHelper.get(app, '/api/organizations?name=Test', superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('organizations');
      expect(Array.isArray(response.body.organizations)).toBe(true);
    });
  });

  describe('GET /api/organizations/:id', () => {
    it('should return specific organization for authorized user', async () => {
      const orgId = seededData.organizations[0]._id.toString();
      
      const response = await AuthHelper.get(app, `/api/organizations/${orgId}`, regularUser)
        .expect(200);

      expect(response.body).toHaveProperty('_id', orgId);
      expect(response.body).toHaveProperty('name');
    });

    it('should allow super admin to access any organization', async () => {
      const orgId = seededData.organizations[1]._id.toString();
      
      const response = await AuthHelper.get(app, `/api/organizations/${orgId}`, superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('_id', orgId);
    });
  });

  describe('POST /api/organizations', () => {
    const organizationData = {
      name: 'New Test Organization',
      display_name: 'New Test Org',
      auth0_id: 'new-test-org-auth0-id',
      description: 'A new test organization'
    };

    it('should create organization for admin user', async () => {
      const response = await AuthHelper.post(app, '/api/organizations', organizationData, adminUser)
        .expect(201);

      expect(response.body).toHaveProperty('name', organizationData.name);
      expect(response.body).toHaveProperty('display_name', organizationData.display_name);
      expect(response.body).toHaveProperty('auth0_id', organizationData.auth0_id);
    });

    it('should deny organization creation for regular user', async () => {
      await AuthHelper.post(app, '/api/organizations', organizationData, regularUser)
        .expect(403);
    });
  });

  describe('PUT /api/organizations/:id', () => {
    it('should update organization for admin user', async () => {
      const orgId = seededData.organizations[0]._id.toString();
      const updateData = {
        display_name: 'Updated Organization Name',
        description: 'Updated description'
      };

      const response = await AuthHelper.put(app, `/api/organizations/${orgId}`, updateData, adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('display_name', updateData.display_name);
      expect(response.body).toHaveProperty('description', updateData.description);
    });

    it('should deny update for regular user', async () => {
      const orgId = seededData.organizations[0]._id.toString();
      const updateData = {
        display_name: 'Unauthorized Update'
      };

      await AuthHelper.put(app, `/api/organizations/${orgId}`, updateData, regularUser)
        .expect(403);
    });
  });

  describe('DELETE /api/organizations/:id', () => {
    it('should delete organization for super admin', async () => {
      // Create a test organization first
      const createResponse = await AuthHelper.post(app, '/api/organizations', {
        name: 'To Be Deleted Org',
        display_name: 'To Be Deleted',
        auth0_id: 'delete-test-org'
      }, superAdminUser);

      const orgId = createResponse.body._id;

      // Delete the organization
      await AuthHelper.delete(app, `/api/organizations/${orgId}`, superAdminUser)
        .expect(200);

      // Verify it's deleted
      await AuthHelper.get(app, `/api/organizations/${orgId}`, superAdminUser)
        .expect(404);
    });

    it('should deny deletion for regular admin', async () => {
      const orgId = seededData.organizations[0]._id.toString();

      await AuthHelper.delete(app, `/api/organizations/${orgId}`, adminUser)
        .expect(403);
    });
  });

  describe('Role-Based Access Control', () => {
    it('should enforce role-based filtering correctly', async () => {
      // Regular user should only see their org
      const regularResponse = await AuthHelper.get(app, '/api/organizations', regularUser)
        .expect(200);

      expect(regularResponse.body.organizations.length).toBe(1);
      expect(regularResponse.body.organizations[0].auth0_id).toBe(regularUser.org_id);

      // Super admin should see all orgs
      const adminResponse = await AuthHelper.get(app, '/api/organizations', superAdminUser)
        .expect(200);

      expect(adminResponse.body.organizations.length).toBeGreaterThan(1);
    });
  });
});
