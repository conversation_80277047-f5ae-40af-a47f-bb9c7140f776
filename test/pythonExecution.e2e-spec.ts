import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { PythonExecutionModule } from '../src/domains/pythonExecution/pythonExecution.module';
import { PythonExecutionController } from '../src/domains/pythonExecution/pythonExecution.controller';
import { PythonExecutionService } from '../src/domains/pythonExecution/pythonExecution.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('PythonExecution API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up PythonExecution API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      PythonExecutionModule,
      UtilsModule
    ], [
      PythonExecutionController
    ], [
      PythonExecutionService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString(),
      permissions: ['execute:python', 'run:simulations']
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      permissions: ['execute:python', 'run:simulations', 'admin:python']
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString(),
      permissions: ['execute:python', 'run:simulations']
    });

    console.log('✅ PythonExecution API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down PythonExecution API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /python/liveSimulation', () => {
    it('should execute live simulation script with valid parameters', async () => {
      const queryParams = {
        longitude: -74.0059,
        latitude: 40.7128,
        range: 1000
      };

      const response = await AuthHelper.get(
        app, 
        `/python/liveSimulation?longitude=${queryParams.longitude}&latitude=${queryParams.latitude}&range=${queryParams.range}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('message', 'Python script execution started');
    });

    it('should handle missing required parameters', async () => {
      // Missing longitude parameter
      const response = await AuthHelper.get(
        app, 
        '/python/liveSimulation?latitude=40.7128&range=1000', 
        testUser
      ).expect(400);

      // The exact error handling depends on the validation implementation
      expect(response.body).toBeDefined();
    });

    it('should handle invalid parameter types', async () => {
      const response = await AuthHelper.get(
        app, 
        '/python/liveSimulation?longitude=invalid&latitude=40.7128&range=1000', 
        testUser
      ).expect(400);

      expect(response.body).toBeDefined();
    });

    it('should validate coordinate ranges', async () => {
      // Invalid latitude (outside -90 to 90 range)
      const response = await AuthHelper.get(
        app, 
        '/python/liveSimulation?longitude=-74.0059&latitude=91&range=1000', 
        testUser
      ).expect(400);

      expect(response.body).toBeDefined();
    });

    it('should validate range parameter', async () => {
      // Negative range should be invalid
      const response = await AuthHelper.get(
        app, 
        '/python/liveSimulation?longitude=-74.0059&latitude=40.7128&range=-100', 
        testUser
      ).expect(400);

      expect(response.body).toBeDefined();
    });

    it('should deny access for user without permissions', async () => {
      const userWithoutPermissions = AuthHelper.createTestUser({
        org_id: seededData.organizations[0]._id.toString(),
        permissions: [] // No python execution permissions
      });

      await AuthHelper.get(
        app, 
        '/python/liveSimulation?longitude=-74.0059&latitude=40.7128&range=1000', 
        userWithoutPermissions
      ).expect(403);
    });
  });

  describe('GET /python/heartBeatSimulation', () => {
    it('should execute heartbeat simulation script with valid parameters', async () => {
      const queryParams = {
        latitude: '40.7128',
        longitude: '-74.0059',
        length: '60',
        time_gap: '5',
        status: 'active'
      };

      const response = await AuthHelper.get(
        app, 
        `/python/heartBeatSimulation?latitude=${queryParams.latitude}&longitude=${queryParams.longitude}&length=${queryParams.length}&time_gap=${queryParams.time_gap}&status=${queryParams.status}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('message', 'Python script execution started');
    });

    it('should handle missing required parameters', async () => {
      // Missing status parameter
      const response = await AuthHelper.get(
        app, 
        '/python/heartBeatSimulation?latitude=40.7128&longitude=-74.0059&length=60&time_gap=5', 
        testUser
      ).expect(400);

      expect(response.body).toBeDefined();
    });

    it('should validate string parameter formats', async () => {
      const queryParams = {
        latitude: '40.7128',
        longitude: '-74.0059',
        length: '60',
        time_gap: '5',
        status: 'active'
      };

      const response = await AuthHelper.get(
        app, 
        `/python/heartBeatSimulation?latitude=${queryParams.latitude}&longitude=${queryParams.longitude}&length=${queryParams.length}&time_gap=${queryParams.time_gap}&status=${queryParams.status}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('message', 'Python script execution started');
    });

    it('should validate status parameter values', async () => {
      // Invalid status value
      const response = await AuthHelper.get(
        app, 
        '/python/heartBeatSimulation?latitude=40.7128&longitude=-74.0059&length=60&time_gap=5&status=invalid_status', 
        testUser
      ).expect(400);

      expect(response.body).toBeDefined();
    });

    it('should validate numeric string parameters', async () => {
      // Invalid length parameter (non-numeric string)
      const response = await AuthHelper.get(
        app, 
        '/python/heartBeatSimulation?latitude=40.7128&longitude=-74.0059&length=invalid&time_gap=5&status=active', 
        testUser
      ).expect(400);

      expect(response.body).toBeDefined();
    });

    it('should deny access for user without permissions', async () => {
      const userWithoutPermissions = AuthHelper.createTestUser({
        org_id: seededData.organizations[0]._id.toString(),
        permissions: [] // No python execution permissions
      });

      await AuthHelper.get(
        app, 
        '/python/heartBeatSimulation?latitude=40.7128&longitude=-74.0059&length=60&time_gap=5&status=active', 
        userWithoutPermissions
      ).expect(403);
    });
  });

  describe('Permission-Based Access Control', () => {
    it('should allow execution for users with proper permissions', async () => {
      // Test user has execute:python permission
      const response = await AuthHelper.get(
        app, 
        '/python/liveSimulation?longitude=-74.0059&latitude=40.7128&range=1000', 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('message', 'Python script execution started');
    });

    it('should allow execution for admin users', async () => {
      // Admin user has all python permissions
      const response = await AuthHelper.get(
        app, 
        '/python/heartBeatSimulation?latitude=40.7128&longitude=-74.0059&length=60&time_gap=5&status=active', 
        adminUser
      ).expect(200);

      expect(response.body).toHaveProperty('message', 'Python script execution started');
    });

    it('should work across different organizations', async () => {
      // User from different organization should also be able to execute
      const response = await AuthHelper.get(
        app, 
        '/python/liveSimulation?longitude=-74.0059&latitude=40.7128&range=1000', 
        otherOrgUser
      ).expect(200);

      expect(response.body).toHaveProperty('message', 'Python script execution started');
    });
  });

  describe('Script Execution Validation', () => {
    it('should handle concurrent script executions', async () => {
      const promises = [
        AuthHelper.get(app, '/python/liveSimulation?longitude=-74.0059&latitude=40.7128&range=1000', testUser),
        AuthHelper.get(app, '/python/heartBeatSimulation?latitude=40.7128&longitude=-74.0059&length=60&time_gap=5&status=active', testUser)
      ];

      const responses = await Promise.all(promises);
      
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('message', 'Python script execution started');
      });
    });

    it('should return consistent response format', async () => {
      const liveSimResponse = await AuthHelper.get(
        app, 
        '/python/liveSimulation?longitude=-74.0059&latitude=40.7128&range=1000', 
        testUser
      ).expect(200);

      const heartbeatResponse = await AuthHelper.get(
        app, 
        '/python/heartBeatSimulation?latitude=40.7128&longitude=-74.0059&length=60&time_gap=5&status=active', 
        testUser
      ).expect(200);

      // Both endpoints should return the same message format
      expect(liveSimResponse.body).toHaveProperty('message', 'Python script execution started');
      expect(heartbeatResponse.body).toHaveProperty('message', 'Python script execution started');
    });
  });
});
