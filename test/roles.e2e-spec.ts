import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { RolesController } from '../src/domains/roles/roles.controller';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('Roles API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let regularUser: TestUser;
  let adminUser: TestUser;
  let superAdminUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Roles API e2e tests...');

    // Setup test environment with required modules
    // Note: Roles controller doesn't have a module/service, it's standalone
    context = await TestSetup.setupE2ETest([
      UtilsModule
    ], [
      RolesController
    ], []);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users with different role levels
    regularUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['user']
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['admin'],
      permissions: ['read:roles', 'manage:roles']
    });

    superAdminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      roles: ['super-admin', 'ad-admin'],
      permissions: ['read:roles', 'manage:roles', 'admin:roles']
    });

    console.log('✅ Roles API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Roles API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /roles', () => {
    it('should return roles for admin user', async () => {
      const response = await AuthHelper.get(app, '/roles', adminUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      // Should contain standard Auth0 roles
      const roleNames = response.body.map(role => role.name);
      expect(roleNames).toContain('admin');
      expect(roleNames).toContain('user');
    });

    it('should deny roles access for regular user', async () => {
      await AuthHelper.get(app, '/roles', regularUser)
        .expect(403);
    });

    it('should return roles with proper structure', async () => {
      const response = await AuthHelper.get(app, '/roles', adminUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      
      if (response.body.length > 0) {
        const role = response.body[0];
        expect(role).toHaveProperty('id');
        expect(role).toHaveProperty('name');
        expect(role).toHaveProperty('description');
      }
    });
  });

  describe('GET /roles/:id', () => {
    it('should return specific role for admin user', async () => {
      // First get roles to find a valid ID
      const rolesResponse = await AuthHelper.get(app, '/roles', adminUser);
      
      if (rolesResponse.body.length > 0) {
        const roleId = rolesResponse.body[0].id;
        
        const response = await AuthHelper.get(app, `/roles/${roleId}`, adminUser)
          .expect(200);

        expect(response.body).toHaveProperty('id', roleId);
        expect(response.body).toHaveProperty('name');
        expect(response.body).toHaveProperty('description');
      }
    });

    it('should deny specific role access for regular user', async () => {
      const roleId = 'test-role-id';
      
      await AuthHelper.get(app, `/roles/${roleId}`, regularUser)
        .expect(403);
    });

    it('should return 404 for non-existent role', async () => {
      const nonExistentRoleId = 'non-existent-role-123';
      
      await AuthHelper.get(app, `/roles/${nonExistentRoleId}`, adminUser)
        .expect(404);
    });
  });

  describe('POST /roles', () => {
    const roleData = {
      name: 'test-role',
      description: 'A test role for e2e testing'
    };

    it('should create role for super admin user', async () => {
      const response = await AuthHelper.post(app, '/roles', roleData, superAdminUser)
        .expect(201);

      expect(response.body).toHaveProperty('name', roleData.name);
      expect(response.body).toHaveProperty('description', roleData.description);
      expect(response.body).toHaveProperty('id');
    });

    it('should deny role creation for regular admin', async () => {
      await AuthHelper.post(app, '/roles', roleData, adminUser)
        .expect(403);
    });

    it('should deny role creation for regular user', async () => {
      await AuthHelper.post(app, '/roles', roleData, regularUser)
        .expect(403);
    });

    it('should validate role data', async () => {
      const invalidRoleData = {
        name: '', // Empty name should be invalid
        description: 'Invalid role'
      };

      await AuthHelper.post(app, '/roles', invalidRoleData, superAdminUser)
        .expect(400);
    });
  });

  describe('PUT /roles/:id', () => {
    it('should update role for super admin user', async () => {
      // First create a role
      const createResponse = await AuthHelper.post(app, '/roles', {
        name: 'update-test-role',
        description: 'Role to be updated'
      }, superAdminUser);

      const roleId = createResponse.body.id;

      // Then update it
      const updateData = {
        name: 'updated-test-role',
        description: 'Updated role description'
      };

      const response = await AuthHelper.put(app, `/roles/${roleId}`, updateData, superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('name', updateData.name);
      expect(response.body).toHaveProperty('description', updateData.description);
    });

    it('should deny role update for regular admin', async () => {
      const roleId = 'test-role-id';
      const updateData = {
        name: 'unauthorized-update',
        description: 'Should not be allowed'
      };

      await AuthHelper.put(app, `/roles/${roleId}`, updateData, adminUser)
        .expect(403);
    });
  });

  describe('DELETE /roles/:id', () => {
    it('should delete role for super admin user', async () => {
      // First create a role
      const createResponse = await AuthHelper.post(app, '/roles', {
        name: 'delete-test-role',
        description: 'Role to be deleted'
      }, superAdminUser);

      const roleId = createResponse.body.id;

      // Then delete it
      await AuthHelper.delete(app, `/roles/${roleId}`, superAdminUser)
        .expect(200);

      // Verify it's deleted
      await AuthHelper.get(app, `/roles/${roleId}`, superAdminUser)
        .expect(404);
    });

    it('should deny role deletion for regular admin', async () => {
      const roleId = 'test-role-id';

      await AuthHelper.delete(app, `/roles/${roleId}`, adminUser)
        .expect(403);
    });

    it('should prevent deletion of system roles', async () => {
      // Try to delete a system role like 'admin' or 'user'
      const rolesResponse = await AuthHelper.get(app, '/roles', superAdminUser);
      const systemRole = rolesResponse.body.find(role => role.name === 'admin' || role.name === 'user');
      
      if (systemRole) {
        await AuthHelper.delete(app, `/roles/${systemRole.id}`, superAdminUser)
          .expect(400); // Should prevent deletion of system roles
      }
    });
  });

  describe('GET /roles/:id/permissions', () => {
    it('should return role permissions for admin user', async () => {
      // First get roles to find a valid ID
      const rolesResponse = await AuthHelper.get(app, '/roles', adminUser);
      
      if (rolesResponse.body.length > 0) {
        const roleId = rolesResponse.body[0].id;
        
        const response = await AuthHelper.get(app, `/roles/${roleId}/permissions`, adminUser)
          .expect(200);

        expect(Array.isArray(response.body)).toBe(true);
      }
    });

    it('should deny role permissions access for regular user', async () => {
      const roleId = 'test-role-id';
      
      await AuthHelper.get(app, `/roles/${roleId}/permissions`, regularUser)
        .expect(403);
    });
  });

  describe('POST /roles/:id/permissions', () => {
    const permissionData = {
      permissions: ['read:test', 'write:test']
    };

    it('should assign permissions to role for super admin', async () => {
      // First create a role
      const createResponse = await AuthHelper.post(app, '/roles', {
        name: 'permission-test-role',
        description: 'Role for permission testing'
      }, superAdminUser);

      const roleId = createResponse.body.id;

      // Then assign permissions
      const response = await AuthHelper.post(app, `/roles/${roleId}/permissions`, permissionData, superAdminUser)
        .expect(200);

      expect(response.body).toHaveProperty('permissions');
      expect(Array.isArray(response.body.permissions)).toBe(true);
    });

    it('should deny permission assignment for regular admin', async () => {
      const roleId = 'test-role-id';

      await AuthHelper.post(app, `/roles/${roleId}/permissions`, permissionData, adminUser)
        .expect(403);
    });
  });

  describe('Role-Based Access Control', () => {
    it('should enforce proper role management permissions', async () => {
      // Regular user should be denied access to all role endpoints
      await AuthHelper.get(app, '/roles', regularUser).expect(403);
      
      // Admin should have read access but not write access
      await AuthHelper.get(app, '/roles', adminUser).expect(200);
      await AuthHelper.post(app, '/roles', { name: 'test', description: 'test' }, adminUser).expect(403);
      
      // Super admin should have full access
      await AuthHelper.get(app, '/roles', superAdminUser).expect(200);
    });
  });
});
