import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { SensorModule } from '../src/domains/sensors/sensor.module';
import { SensorController } from '../src/domains/sensors/sensor.controller';
import { SensorService } from '../src/domains/sensors/sensor.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('Sensors (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let adminUser: TestUser;
  let readOnlyUser: TestUser;
  let unauthorizedUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Sensors e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      SensorModule,
      UtilsModule
    ], [
      SensorController
    ], [
      SensorService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users with different permission levels for sensor testing
    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString(),
      permissions: ['read:sensors', 'assign:sensors', 'update:rename-sensors']
    });

    readOnlyUser = AuthHelper.createReadOnlyUser({
      org_id: seededData.organizations[0]._id.toString(),
      permissions: ['read:sensors']
    });

    unauthorizedUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString(),
      permissions: ['read:other-resources'] // No sensor permissions
    });

    console.log('✅ Sensors e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Sensors e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/sensors', () => {
    it('should return sensors for user with read:sensors permission', async () => {
      const response = await AuthHelper.get(app, '/api/sensors', adminUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should return sensors for read-only user', async () => {
      const response = await AuthHelper.get(app, '/api/sensors', readOnlyUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });

    it('should deny access for user without read:sensors permission', async () => {
      await AuthHelper.get(app, '/api/sensors', unauthorizedUser)
        .expect(403);
    });
  });

  describe('POST /api/sensors', () => {
    const sensorData = {
      name: 'Test Sensor',
      type: 'temperature',
      location: { lat: 40.7128, lon: -74.0060 }
    };

    it('should allow sensor assignment for user with assign:sensors permission', async () => {
      const response = await AuthHelper.post(app, '/api/sensors', sensorData, adminUser)
        .expect(201);

      expect(response.body).toHaveProperty('name', sensorData.name);
    });

    it('should deny sensor assignment for read-only user', async () => {
      await AuthHelper.post(app, '/api/sensors', sensorData, readOnlyUser)
        .expect(403);
    });

    it('should deny sensor assignment for unauthorized user', async () => {
      await AuthHelper.post(app, '/api/sensors', sensorData, unauthorizedUser)
        .expect(403);
    });
  });

  describe('POST /api/sensors/rename', () => {
    const renameData = {
      sensorId: 'test-sensor-id',
      newName: 'Renamed Sensor'
    };

    it('should allow sensor renaming for user with update:rename-sensors permission', async () => {
      // First create a sensor to rename
      const createResponse = await AuthHelper.post(app, '/api/sensors', {
        name: 'Original Sensor',
        type: 'temperature'
      }, adminUser);

      const sensorId = createResponse.body._id;

      const response = await AuthHelper.post(app, '/api/sensors/rename', {
        sensorId,
        newName: 'Renamed Sensor'
      }, adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('name', 'Renamed Sensor');
    });

    it('should deny sensor renaming for read-only user', async () => {
      await AuthHelper.post(app, '/api/sensors/rename', renameData, readOnlyUser)
        .expect(403);
    });

    it('should deny sensor renaming for unauthorized user', async () => {
      await AuthHelper.post(app, '/api/sensors/rename', renameData, unauthorizedUser)
        .expect(403);
    });
  });

  describe('Authorization Edge Cases', () => {
    it('should handle missing permissions gracefully', async () => {
      const userWithoutPermissions = AuthHelper.createTestUser({
        org_id: seededData.organizations[0]._id.toString(),
        permissions: [] // No permissions
      });

      await AuthHelper.get(app, '/api/sensors', userWithoutPermissions)
        .expect(403);
    });
  });
});
