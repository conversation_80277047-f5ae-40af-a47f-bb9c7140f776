import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { ServiceZoneModule } from '../src/domains/serviceZone/serviceZone.module';
import { ServiceZonesController } from '../src/domains/serviceZone/serviceZone.controller';
import { ServiceZoneService } from '../src/domains/serviceZone/serviceZone.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('ServiceZone API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up ServiceZone API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      ServiceZoneModule,
      UtilsModule
    ], [
      ServiceZonesController
    ], [
      ServiceZoneService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ ServiceZone API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down ServiceZone API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/serviceZones', () => {
    it('should return service zones for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/serviceZones', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('serviceZones');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.serviceZones)).toBe(true);
    });

    it('should support pagination parameters', async () => {
      const response = await AuthHelper.get(app, '/api/serviceZones?page=1&pageSize=10', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('page', 1);
      expect(response.body.serviceZones.length).toBeLessThanOrEqual(10);
    });

    it('should support name filtering', async () => {
      const response = await AuthHelper.get(app, '/api/serviceZones?name=Test', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('serviceZones');
      expect(Array.isArray(response.body.serviceZones)).toBe(true);
    });

    it('should support FIPS code filtering', async () => {
      const response = await AuthHelper.get(app, '/api/serviceZones?fips=12345', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('serviceZones');
      expect(Array.isArray(response.body.serviceZones)).toBe(true);
    });
  });

  describe('GET /api/serviceZones/:id', () => {
    it('should return specific service zone', async () => {
      const serviceZoneId = seededData.serviceZones[0]._id.toString();
      
      const response = await AuthHelper.get(app, `/api/serviceZones/${serviceZoneId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('_id', serviceZoneId);
      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('fips');
    });

    it('should return 404 for non-existent service zone', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';
      
      await AuthHelper.get(app, `/api/serviceZones/${nonExistentId}`, testUser)
        .expect(404);
    });
  });

  describe('POST /api/serviceZones', () => {
    const serviceZoneData = {
      name: 'New Test Service Zone',
      fips: ['12345', '67890'],
      description: 'A test service zone',
      coordinates: {
        type: 'Polygon',
        coordinates: [[
          [-74.0059, 40.7128],
          [-74.0059, 40.7628],
          [-73.9559, 40.7628],
          [-73.9559, 40.7128],
          [-74.0059, 40.7128]
        ]]
      }
    };

    it('should create service zone for admin user', async () => {
      const response = await AuthHelper.post(app, '/api/serviceZones', serviceZoneData, adminUser)
        .expect(201);

      expect(response.body).toHaveProperty('name', serviceZoneData.name);
      expect(response.body).toHaveProperty('fips');
      expect(response.body.fips).toEqual(serviceZoneData.fips);
      expect(response.body).toHaveProperty('org_id', adminUser.org_id);
    });

    it('should deny service zone creation for regular user without permissions', async () => {
      await AuthHelper.post(app, '/api/serviceZones', serviceZoneData, testUser)
        .expect(403);
    });
  });

  describe('PUT /api/serviceZones/:id', () => {
    it('should update service zone for admin user', async () => {
      const serviceZoneId = seededData.serviceZones[0]._id.toString();
      const updateData = {
        name: 'Updated Service Zone Name',
        description: 'Updated description'
      };

      const response = await AuthHelper.put(app, `/api/serviceZones/${serviceZoneId}`, updateData, adminUser)
        .expect(200);

      expect(response.body).toHaveProperty('name', updateData.name);
      expect(response.body).toHaveProperty('description', updateData.description);
    });

    it('should deny update for regular user', async () => {
      const serviceZoneId = seededData.serviceZones[0]._id.toString();
      const updateData = {
        name: 'Unauthorized Update'
      };

      await AuthHelper.put(app, `/api/serviceZones/${serviceZoneId}`, updateData, testUser)
        .expect(403);
    });
  });

  describe('DELETE /api/serviceZones/:id', () => {
    it('should delete service zone for admin user', async () => {
      // Create a test service zone first
      const createResponse = await AuthHelper.post(app, '/api/serviceZones', {
        name: 'To Be Deleted Zone',
        fips: ['99999'],
        description: 'Zone to be deleted'
      }, adminUser);

      const serviceZoneId = createResponse.body._id;

      // Delete the service zone
      await AuthHelper.delete(app, `/api/serviceZones/${serviceZoneId}`, adminUser)
        .expect(200);

      // Verify it's deleted
      await AuthHelper.get(app, `/api/serviceZones/${serviceZoneId}`, adminUser)
        .expect(404);
    });

    it('should deny deletion for regular user', async () => {
      const serviceZoneId = seededData.serviceZones[0]._id.toString();

      await AuthHelper.delete(app, `/api/serviceZones/${serviceZoneId}`, testUser)
        .expect(403);
    });
  });

  describe('GET /api/serviceZones/user/subscriptions', () => {
    it('should return user subscriptions', async () => {
      const response = await AuthHelper.get(app, '/api/serviceZones/user/subscriptions', testUser)
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
    });
  });

  describe('POST /api/serviceZones/user/subscribe/:id', () => {
    it('should allow user to subscribe to service zone', async () => {
      const serviceZoneId = seededData.serviceZones[0]._id.toString();

      const response = await AuthHelper.post(app, `/api/serviceZones/user/subscribe/${serviceZoneId}`, {}, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('message');
    });

    it('should handle subscription to non-existent service zone', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';

      await AuthHelper.post(app, `/api/serviceZones/user/subscribe/${nonExistentId}`, {}, testUser)
        .expect(404);
    });
  });

  describe('DELETE /api/serviceZones/user/unsubscribe/:id', () => {
    it('should allow user to unsubscribe from service zone', async () => {
      const serviceZoneId = seededData.serviceZones[0]._id.toString();

      // First subscribe
      await AuthHelper.post(app, `/api/serviceZones/user/subscribe/${serviceZoneId}`, {}, testUser);

      // Then unsubscribe
      const response = await AuthHelper.delete(app, `/api/serviceZones/user/unsubscribe/${serviceZoneId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('message');
    });
  });

  describe('Geographic Queries', () => {
    it('should support geographic filtering with coordinates', async () => {
      const lat = 40.7128;
      const lng = -74.0059;

      const response = await AuthHelper.get(app, `/api/serviceZones?lat=${lat}&lng=${lng}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('serviceZones');
      expect(Array.isArray(response.body.serviceZones)).toBe(true);
    });
  });

  describe('Organization Isolation', () => {
    it('should return different service zones for different organizations', async () => {
      const org1Response = await AuthHelper.get(app, '/api/serviceZones', testUser)
        .expect(200);

      const org2Response = await AuthHelper.get(app, '/api/serviceZones', otherOrgUser)
        .expect(200);

      // Both should have valid structure but potentially different data
      expect(org1Response.body).toHaveProperty('serviceZones');
      expect(org2Response.body).toHaveProperty('serviceZones');
      expect(Array.isArray(org1Response.body.serviceZones)).toBe(true);
      expect(Array.isArray(org2Response.body.serviceZones)).toBe(true);
    });
  });
});
