import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { Auth<PERSON>elper, TestUser } from './utils/auth-helper';
import { SpectrumEventModule } from '../src/domains/spectrumEvents/spectrumEvent.module';
import { SpectrumEventController } from '../src/domains/spectrumEvents/spectrumEvent.controller';
import { SpectrumEventService } from '../src/domains/spectrumEvents/spectrumEvent.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('SpectrumEvents API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up SpectrumEvents API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      SpectrumEventModule,
      UtilsModule
    ], [
      SpectrumEventController
    ], [
      SpectrumEventService
    ]);

    app = context.app;
    
    // Wait for database connection (including spectrum database)
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ SpectrumEvents API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down SpectrumEvents API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/spectrum-events', () => {
    it('should return spectrum events for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/spectrum-events', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('events');
      expect(response.body).toHaveProperty('total');
      expect(response.body).toHaveProperty('page');
      expect(response.body).toHaveProperty('totalPages');
      expect(Array.isArray(response.body.events)).toBe(true);
    });

    it('should support pagination parameters', async () => {
      const response = await AuthHelper.get(app, '/api/spectrum-events?page=1&pageSize=10', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('page', 1);
      expect(response.body.events.length).toBeLessThanOrEqual(10);
    });

    it('should support date range filtering', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-12-31';
      
      const response = await AuthHelper.get(
        app, 
        `/api/spectrum-events?startDate=${startDate}&endDate=${endDate}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('events');
      expect(Array.isArray(response.body.events)).toBe(true);
    });

    it('should support frequency filtering', async () => {
      const minFreq = 2400;
      const maxFreq = 2500;
      
      const response = await AuthHelper.get(
        app, 
        `/api/spectrum-events?minFrequency=${minFreq}&maxFrequency=${maxFreq}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('events');
      expect(Array.isArray(response.body.events)).toBe(true);
    });

    it('should support device ID filtering', async () => {
      const deviceId = 'test-spectrum-device-001';
      
      const response = await AuthHelper.get(app, `/api/spectrum-events?deviceId=${deviceId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('events');
      expect(Array.isArray(response.body.events)).toBe(true);
    });

    it('should support signal strength filtering', async () => {
      const minSignalStrength = -80;
      const maxSignalStrength = -20;
      
      const response = await AuthHelper.get(
        app, 
        `/api/spectrum-events?minSignalStrength=${minSignalStrength}&maxSignalStrength=${maxSignalStrength}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('events');
      expect(Array.isArray(response.body.events)).toBe(true);
    });

    it('should support location-based filtering', async () => {
      const lat = 40.7128;
      const lng = -74.0059;
      const radius = 1000; // meters
      
      const response = await AuthHelper.get(
        app, 
        `/api/spectrum-events?lat=${lat}&lng=${lng}&radius=${radius}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('events');
      expect(Array.isArray(response.body.events)).toBe(true);
    });

    it('should support combined filtering', async () => {
      const response = await AuthHelper.get(
        app, 
        '/api/spectrum-events?page=1&pageSize=5&minFrequency=2400&maxFrequency=2500&startDate=2024-01-01', 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('events');
      expect(response.body).toHaveProperty('page', 1);
      expect(response.body.events.length).toBeLessThanOrEqual(5);
    });
  });

  describe('GET /api/spectrum-events/:id', () => {
    it('should return specific spectrum event', async () => {
      // First get events to find a valid ID
      const eventsResponse = await AuthHelper.get(app, '/api/spectrum-events?pageSize=1', testUser);
      
      if (eventsResponse.body.events.length > 0) {
        const eventId = eventsResponse.body.events[0]._id;
        
        const response = await AuthHelper.get(app, `/api/spectrum-events/${eventId}`, testUser)
          .expect(200);

        expect(response.body).toHaveProperty('_id', eventId);
        expect(response.body).toHaveProperty('frequency');
        expect(response.body).toHaveProperty('signalStrength');
        expect(response.body).toHaveProperty('timestamp');
      }
    });

    it('should return 404 for non-existent spectrum event', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';
      
      await AuthHelper.get(app, `/api/spectrum-events/${nonExistentId}`, testUser)
        .expect(404);
    });
  });

  describe('GET /api/spectrum-events/stats/overview', () => {
    it('should return spectrum events statistics overview', async () => {
      const response = await AuthHelper.get(app, '/api/spectrum-events/stats/overview', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('totalEvents');
      expect(response.body).toHaveProperty('uniqueDevices');
      expect(response.body).toHaveProperty('frequencyDistribution');
      expect(response.body).toHaveProperty('signalStrengthDistribution');
      expect(response.body).toHaveProperty('eventsOverTime');
      expect(typeof response.body.totalEvents).toBe('number');
      expect(typeof response.body.uniqueDevices).toBe('number');
      expect(Array.isArray(response.body.frequencyDistribution)).toBe(true);
      expect(Array.isArray(response.body.signalStrengthDistribution)).toBe(true);
      expect(Array.isArray(response.body.eventsOverTime)).toBe(true);
    });

    it('should support date range filtering in statistics', async () => {
      const startDate = '2024-01-01';
      const endDate = '2024-06-30';
      
      const response = await AuthHelper.get(
        app, 
        `/api/spectrum-events/stats/overview?startDate=${startDate}&endDate=${endDate}`, 
        testUser
      ).expect(200);

      expect(response.body).toHaveProperty('totalEvents');
      expect(response.body).toHaveProperty('frequencyDistribution');
    });
  });

  describe('GET /api/spectrum-events/device/:deviceId', () => {
    it('should return spectrum events for specific device', async () => {
      const deviceId = 'test-spectrum-device-001';
      
      const response = await AuthHelper.get(app, `/api/spectrum-events/device/${deviceId}`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('events');
      expect(response.body).toHaveProperty('total');
      expect(Array.isArray(response.body.events)).toBe(true);
      
      // All events should be for the specified device
      response.body.events.forEach(event => {
        expect(event.deviceId).toBe(deviceId);
      });
    });

    it('should support pagination for device events', async () => {
      const deviceId = 'test-spectrum-device-001';
      
      const response = await AuthHelper.get(app, `/api/spectrum-events/device/${deviceId}?page=1&pageSize=5`, testUser)
        .expect(200);

      expect(response.body).toHaveProperty('events');
      expect(response.body.events.length).toBeLessThanOrEqual(5);
    });
  });

  describe('Organization Isolation', () => {
    it('should return different spectrum events for different organizations', async () => {
      const org1Response = await AuthHelper.get(app, '/api/spectrum-events', testUser)
        .expect(200);

      const org2Response = await AuthHelper.get(app, '/api/spectrum-events', otherOrgUser)
        .expect(200);

      // Both should have valid structure but potentially different data
      expect(org1Response.body).toHaveProperty('events');
      expect(org2Response.body).toHaveProperty('events');
      expect(Array.isArray(org1Response.body.events)).toBe(true);
      expect(Array.isArray(org2Response.body.events)).toBe(true);
    });
  });
});
