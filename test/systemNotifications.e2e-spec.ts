import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { Auth<PERSON>elper, TestUser } from './utils/auth-helper';
import { SystemNotificationModule } from '../src/domains/systemNotifications/systemNotification.module';
import { SystemNotificationsController } from '../src/domains/systemNotifications/systemNotification.controller';
import { SystemNotificationService } from '../src/domains/systemNotifications/systemNotification.service';
import { UtilsModule } from '../src/utils/UtilsModule';

describe('SystemNotifications API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up SystemNotifications API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      SystemNotificationModule,
      UtilsModule
    ], [
      SystemNotificationsController
    ], [
      SystemNotificationService
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ SystemNotifications API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down SystemNotifications API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /api/system-notifications/me', () => {
    it('should return system notifications for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/api/system-notifications/me', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('notifications');
      expect(response.body).toHaveProperty('pagination');
      expect(Array.isArray(response.body.notifications)).toBe(true);
    });

    it('should support pagination parameters', async () => {
      const response = await AuthHelper.get(app, '/api/system-notifications/me?page=1&pageSize=10', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('notifications');
      expect(response.body).toHaveProperty('pagination');
      expect(response.body.notifications.length).toBeLessThanOrEqual(10);
    });

    it('should only return unseen notifications by default', async () => {
      const response = await AuthHelper.get(app, '/api/system-notifications/me', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('notifications');
      expect(Array.isArray(response.body.notifications)).toBe(true);
      
      // All notifications should be unseen (or the endpoint filters for unseen)
      response.body.notifications.forEach(notification => {
        expect(notification.seen).toBeFalsy();
      });
    });

    it('should return different notifications for different organizations', async () => {
      const org1Response = await AuthHelper.get(app, '/api/system-notifications/me', testUser)
        .expect(200);

      const org2Response = await AuthHelper.get(app, '/api/system-notifications/me', otherOrgUser)
        .expect(200);

      // Both should have valid structure
      expect(org1Response.body).toHaveProperty('notifications');
      expect(org2Response.body).toHaveProperty('notifications');
      expect(Array.isArray(org1Response.body.notifications)).toBe(true);
      expect(Array.isArray(org2Response.body.notifications)).toBe(true);
    });
  });

  describe('PATCH /api/system-notifications/:id', () => {
    it('should mark system notification as seen', async () => {
      // First get notifications to find a valid ID
      const notificationsResponse = await AuthHelper.get(app, '/api/system-notifications/me?pageSize=1', testUser);
      
      if (notificationsResponse.body.notifications.length > 0) {
        const notificationId = notificationsResponse.body.notifications[0]._id;
        
        const response = await AuthHelper.patch(app, `/api/system-notifications/${notificationId}`, {}, testUser)
          .expect(200);

        // The endpoint returns null according to the controller
        expect(response.body).toBeNull();
      }
    });

    it('should return 404 for non-existent system notification', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';
      
      await AuthHelper.patch(app, `/api/system-notifications/${nonExistentId}`, {}, testUser)
        .expect(404);
    });

    it('should not allow marking notifications from other organizations', async () => {
      // Create a notification for one org, try to mark it from another
      const createResponse = await AuthHelper.post(app, '/api/system-notifications', {
        title: 'Test System Notification',
        message: 'Test message for organization isolation',
        type: 'info',
        priority: 'medium'
      }, adminUser);

      const notificationId = createResponse.body._id;

      // Try to mark as seen from different organization
      await AuthHelper.patch(app, `/api/system-notifications/${notificationId}`, {}, otherOrgUser)
        .expect(404);
    });
  });

  describe('PATCH /api/system-notifications', () => {
    it('should mark all system notifications as seen for user', async () => {
      const response = await AuthHelper.patch(app, '/api/system-notifications', {}, testUser)
        .expect(200);

      // The endpoint returns the result of markAllAsSeen
      expect(response.body).toBeDefined();
    });

    it('should only mark notifications for user organization', async () => {
      // Mark all as seen for one user
      const response = await AuthHelper.patch(app, '/api/system-notifications', {}, testUser)
        .expect(200);

      expect(response.body).toBeDefined();

      // Other organization user should still have their notifications
      const otherOrgResponse = await AuthHelper.get(app, '/api/system-notifications/me', otherOrgUser)
        .expect(200);

      expect(otherOrgResponse.body).toHaveProperty('notifications');
    });
  });

  describe('POST /api/system-notifications', () => {
    const systemNotificationData = {
      title: 'Test System Notification',
      message: 'This is a test system notification message',
      type: 'info',
      priority: 'medium',
      metadata: {
        source: 'test',
        category: 'system'
      }
    };

    it('should create system notification for admin user', async () => {
      const response = await AuthHelper.post(app, '/api/system-notifications', systemNotificationData, adminUser)
        .expect(201);

      expect(response.body).toHaveProperty('title', systemNotificationData.title);
      expect(response.body).toHaveProperty('message', systemNotificationData.message);
      expect(response.body).toHaveProperty('type', systemNotificationData.type);
      expect(response.body).toHaveProperty('priority', systemNotificationData.priority);
      expect(response.body).toHaveProperty('createdBy');
      expect(response.body).toHaveProperty('org_id', adminUser.org_id);
    });

    it('should deny system notification creation for regular user without permissions', async () => {
      await AuthHelper.post(app, '/api/system-notifications', systemNotificationData, testUser)
        .expect(403);
    });

    it('should publish notification after creation', async () => {
      const response = await AuthHelper.post(app, '/api/system-notifications', systemNotificationData, adminUser)
        .expect(201);

      // The notification should be created and published
      expect(response.body).toHaveProperty('_id');
      expect(response.body).toHaveProperty('title', systemNotificationData.title);
    });
  });

  describe('Organization Isolation', () => {
    it('should enforce organization isolation for system notifications', async () => {
      // Create notification for one organization
      const createResponse = await AuthHelper.post(app, '/api/system-notifications', {
        title: 'Org 1 Notification',
        message: 'This is for organization 1 only',
        type: 'info',
        priority: 'low'
      }, adminUser);

      expect(createResponse.status).toBe(201);

      // User from same org should see it
      const sameOrgResponse = await AuthHelper.get(app, '/api/system-notifications/me', testUser)
        .expect(200);

      // User from different org should not see it
      const differentOrgResponse = await AuthHelper.get(app, '/api/system-notifications/me', otherOrgUser)
        .expect(200);

      // Both should have valid responses but different content
      expect(sameOrgResponse.body).toHaveProperty('notifications');
      expect(differentOrgResponse.body).toHaveProperty('notifications');
    });
  });
});
