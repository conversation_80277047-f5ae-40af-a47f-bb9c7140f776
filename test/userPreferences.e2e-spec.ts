import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { Auth<PERSON>elper, TestUser } from './utils/auth-helper';
import { UserPreferencesController } from '../src/domains/userPreferences/userPreferences.controller';
import { UserPreferencesModel } from '../src/domains/userPreferences/userPreferences.model';
import { UtilsModule } from '../src/utils/UtilsModule';
import { MongooseModule } from '@nestjs/mongoose';
import UserPreferencesSchema from '../src/domains/userPreferences/userPreferences.schema';

describe('UserPreferences API (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;
  let otherOrgUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up UserPreferences API e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      MongooseModule.forFeature([{ name: 'userPreferences', schema: UserPreferencesSchema }]),
      UtilsModule
    ], [
      UserPreferencesController
    ], [
      UserPreferencesModel
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users for different scenarios
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    // User from different organization for isolation testing
    otherOrgUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[1]._id.toString()
    });

    console.log('✅ UserPreferences API e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down UserPreferences API e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /userPreferences', () => {
    it('should return user preferences for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/userPreferences', testUser)
        .expect(200);

      // Response can be null if no preferences exist, or an object if they do
      if (response.body !== null) {
        expect(response.body).toHaveProperty('user_id');
        expect(response.body).toHaveProperty('org_id');
      }
    });

    it('should return null if no preferences exist for user', async () => {
      // For a new user, preferences might not exist yet
      const newUser = AuthHelper.createTestUser({
        org_id: seededData.organizations[0]._id.toString(),
        sub: 'new-user-with-no-prefs'
      });

      const response = await AuthHelper.get(app, '/userPreferences', newUser)
        .expect(200);

      // Could be null or an empty preferences object
      expect(response.body).toBeDefined();
    });

    it('should return different preferences for different users', async () => {
      const user1Response = await AuthHelper.get(app, '/userPreferences', testUser)
        .expect(200);

      const user2Response = await AuthHelper.get(app, '/userPreferences', otherOrgUser)
        .expect(200);

      // Both should be valid responses
      expect(user1Response.body).toBeDefined();
      expect(user2Response.body).toBeDefined();
    });
  });

  describe('GET /userPreferences/:id', () => {
    it('should return specific user preferences by ID', async () => {
      // First create preferences to get an ID
      const createResponse = await AuthHelper.post(app, '/userPreferences', {
        theme: 'dark',
        language: 'en',
        notifications: {
          email: true,
          push: false,
          sms: true
        },
        dashboard: {
          layout: 'grid',
          widgets: ['alerts', 'drones', 'events']
        }
      }, testUser);

      if (createResponse.body && createResponse.body._id) {
        const preferencesId = createResponse.body._id;
        
        const response = await AuthHelper.get(app, `/userPreferences/${preferencesId}`, testUser)
          .expect(200);

        expect(response.body).toHaveProperty('_id', preferencesId);
        expect(response.body).toHaveProperty('user_id');
      }
    });

    it('should return 404 for non-existent preferences', async () => {
      const nonExistentId = '507f1f77bcf86cd799439011';
      
      await AuthHelper.get(app, `/userPreferences/${nonExistentId}`, testUser)
        .expect(404);
    });
  });

  describe('POST /userPreferences', () => {
    const preferencesData = {
      theme: 'dark',
      language: 'en',
      timezone: 'America/New_York',
      notifications: {
        email: true,
        push: false,
        sms: true,
        alertTypes: ['intrusion', 'system']
      },
      dashboard: {
        layout: 'grid',
        widgets: ['alerts', 'drones', 'events', 'statistics'],
        refreshInterval: 30
      },
      map: {
        defaultZoom: 10,
        defaultCenter: {
          lat: 40.7128,
          lng: -74.0059
        },
        layers: ['satellite', 'traffic']
      }
    };

    it('should create or update user preferences', async () => {
      const response = await AuthHelper.post(app, '/userPreferences', preferencesData, testUser)
        .expect(201);

      expect(response.body).toHaveProperty('theme', preferencesData.theme);
      expect(response.body).toHaveProperty('language', preferencesData.language);
      expect(response.body).toHaveProperty('timezone', preferencesData.timezone);
      expect(response.body).toHaveProperty('notifications');
      expect(response.body).toHaveProperty('dashboard');
      expect(response.body).toHaveProperty('map');
      expect(response.body).toHaveProperty('user_id');
      expect(response.body).toHaveProperty('org_id', testUser.org_id);
    });

    it('should update existing preferences if they exist', async () => {
      // First create preferences
      await AuthHelper.post(app, '/userPreferences', preferencesData, testUser);

      // Then update them
      const updatedPreferences = {
        ...preferencesData,
        theme: 'light',
        language: 'es'
      };

      const response = await AuthHelper.post(app, '/userPreferences', updatedPreferences, testUser)
        .expect(201);

      expect(response.body).toHaveProperty('theme', 'light');
      expect(response.body).toHaveProperty('language', 'es');
    });

    it('should handle partial preference updates', async () => {
      // Create initial preferences
      await AuthHelper.post(app, '/userPreferences', preferencesData, testUser);

      // Update only theme
      const partialUpdate = {
        theme: 'light'
      };

      const response = await AuthHelper.post(app, '/userPreferences', partialUpdate, testUser)
        .expect(201);

      expect(response.body).toHaveProperty('theme', 'light');
      expect(response.body).toHaveProperty('user_id');
    });

    it('should set correct user context', async () => {
      const response = await AuthHelper.post(app, '/userPreferences', preferencesData, testUser)
        .expect(201);

      expect(response.body).toHaveProperty('user_id');
      expect(response.body).toHaveProperty('org_id', testUser.org_id);
    });
  });

  describe('User Isolation', () => {
    it('should isolate preferences between users', async () => {
      const user1Preferences = {
        theme: 'dark',
        language: 'en'
      };

      const user2Preferences = {
        theme: 'light',
        language: 'es'
      };

      // Create preferences for both users
      await AuthHelper.post(app, '/userPreferences', user1Preferences, testUser);
      await AuthHelper.post(app, '/userPreferences', user2Preferences, otherOrgUser);

      // Get preferences for each user
      const user1Response = await AuthHelper.get(app, '/userPreferences', testUser)
        .expect(200);

      const user2Response = await AuthHelper.get(app, '/userPreferences', otherOrgUser)
        .expect(200);

      // Each user should see their own preferences
      if (user1Response.body) {
        expect(user1Response.body).toHaveProperty('theme', 'dark');
        expect(user1Response.body).toHaveProperty('language', 'en');
      }

      if (user2Response.body) {
        expect(user2Response.body).toHaveProperty('theme', 'light');
        expect(user2Response.body).toHaveProperty('language', 'es');
      }
    });
  });
});
