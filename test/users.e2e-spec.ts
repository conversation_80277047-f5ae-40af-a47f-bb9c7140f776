import { INestApplication } from '@nestjs/common';
import { TestSetup, E2ETestContext } from './utils/test-setup';
import { SeededData } from './utils/database-seeder';
import { AuthHelper, TestUser } from './utils/auth-helper';
import { UsersModule } from '../src/domains/users/users.module';
import { UsersController } from '../src/domains/users/users.controller';
import { UsersService } from '../src/domains/users/users.service';
import { UtilsModule } from '../src/utils/UtilsModule';
import { CacheModule } from '@nestjs/cache-manager';

describe('Users (e2e)', () => {
  let context: E2ETestContext;
  let app: INestApplication;
  let seededData: SeededData;
  let testUser: TestUser;
  let adminUser: TestUser;

  beforeAll(async () => {
    console.log('🚀 Setting up Users e2e tests...');

    // Setup test environment with required modules
    context = await TestSetup.setupE2ETest([
      UsersModule,
      UtilsModule,
      CacheModule.register({
        isGlobal: true,
        store: 'memory',
        ttl: 300,
      })
    ]);

    app = context.app;
    
    // Wait for database connection
    await TestSetup.waitForDatabaseConnection(context);
    
    // Seed test data
    seededData = await TestSetup.seedTestData(context);

    // Create test users with different permission levels
    testUser = AuthHelper.createTestUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    adminUser = AuthHelper.createAdminUser({
      org_id: seededData.organizations[0]._id.toString()
    });

    console.log('✅ Users e2e test setup completed');
  }, 120000);

  afterAll(async () => {
    console.log('🧹 Tearing down Users e2e tests...');
    await TestSetup.teardownE2ETest(context);
  });

  beforeEach(async () => {
    // Reseed data for clean state
    seededData = await TestSetup.seedTestData(context);
  });

  describe('GET /users/me', () => {
    it('should return current user info for authenticated user', async () => {
      const response = await AuthHelper.get(app, '/users/me', testUser)
        .expect(200);

      expect(response.body).toHaveProperty('sub', testUser.sub);
      expect(response.body).toHaveProperty('org_id', testUser.org_id);
      expect(response.body).toHaveProperty('email');
    });

    it('should handle different user contexts', async () => {
      // Test with admin user
      const adminResponse = await AuthHelper.get(app, '/users/me', adminUser)
        .expect(200);

      expect(adminResponse.body).toHaveProperty('sub', adminUser.sub);
      expect(adminResponse.body).toHaveProperty('org_id', adminUser.org_id);

      // Test with regular user
      const userResponse = await AuthHelper.get(app, '/users/me', testUser)
        .expect(200);

      expect(userResponse.body).toHaveProperty('sub', testUser.sub);
      expect(userResponse.body).toHaveProperty('org_id', testUser.org_id);
    });

    it('should work with organization-specific users', async () => {
      // Create user for specific organization
      const orgUser = AuthHelper.createOrgUser(seededData.organizations[1]._id.toString());
      
      const response = await AuthHelper.get(app, '/users/me', orgUser)
        .expect(200);

      expect(response.body).toHaveProperty('org_id', orgUser.org_id);
    });
  });

  describe('Authentication Context', () => {
    it('should properly set request context from test headers', async () => {
      // This test verifies that our middleware bypass is working correctly
      const customUser = AuthHelper.createTestUser({
        sub: 'auth0|custom-test-user',
        org_id: 'custom-org-id',
        permissions: ['read:users', 'write:users']
      });

      const response = await AuthHelper.get(app, '/users/me', customUser)
        .expect(200);

      expect(response.body).toHaveProperty('sub', customUser.sub);
      expect(response.body).toHaveProperty('org_id', customUser.org_id);
    });
  });
});
