import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';

export interface TestUser {
  sub: string;
  org_id: string;
  permissions: string[];
  roles: string[];
  name?: string;
  email?: string;
}

export interface TestAuthHeaders {
  'x-test-user-sub': string;
  'x-test-org-id': string;
  'x-test-permissions': string;
  'x-test-roles'?: string;
  [key: string]: string | undefined;
}

export class AuthHelper {
  /**
   * Create default test user for e2e tests
   */
  static createTestUser(overrides: Partial<TestUser> = {}): TestUser {
    return {
      sub: 'auth0|test-user-123',
      org_id: 'test-org-id',
      permissions: ['read:all', 'write:all', 'delete:all'],
      roles: ['admin', 'user'],
      name: 'Test User',
      email: '<EMAIL>',
      ...overrides
    };
  }

  /**
   * Create admin test user with full permissions
   */
  static createAdminUser(overrides: Partial<TestUser> = {}): TestUser {
    return this.createTestUser({
      sub: 'auth0|admin-user-456',
      permissions: [
        'read:all', 'write:all', 'delete:all',
        'read:sensors', 'assign:sensors', 'update:rename-sensors',
        'admin:organizations', 'admin:users'
      ],
      roles: ['admin', 'super-admin'],
      name: 'Admin User',
      email: '<EMAIL>',
      ...overrides
    });
  }

  /**
   * Create limited user with read-only permissions
   */
  static createReadOnlyUser(overrides: Partial<TestUser> = {}): TestUser {
    return this.createTestUser({
      sub: 'auth0|readonly-user-789',
      permissions: ['read:sensors'],
      roles: ['user'],
      name: 'Read Only User',
      email: '<EMAIL>',
      ...overrides
    });
  }

  /**
   * Convert TestUser to headers for HTTP requests
   */
  static userToHeaders(user: TestUser): TestAuthHeaders {
    return {
      'x-test-user-sub': user.sub,
      'x-test-org-id': user.org_id,
      'x-test-permissions': user.permissions.join(','),
      'x-test-roles': user.roles.join(',')
    };
  }

  /**
   * Create authenticated GET request
   */
  static get(app: INestApplication, path: string, user: TestUser = this.createTestUser()) {
    const headers = this.userToHeaders(user);
    return request(app.getHttpServer()).get(path).set(headers);
  }

  /**
   * Create authenticated POST request
   */
  static post(app: INestApplication, path: string, data: any = {}, user: TestUser = this.createTestUser()) {
    const headers = this.userToHeaders(user);
    return request(app.getHttpServer()).post(path).set(headers).send(data);
  }

  /**
   * Create authenticated PUT request
   */
  static put(app: INestApplication, path: string, data: any = {}, user: TestUser = this.createTestUser()) {
    const headers = this.userToHeaders(user);
    return request(app.getHttpServer()).put(path).set(headers).send(data);
  }

  /**
   * Create authenticated PATCH request
   */
  static patch(app: INestApplication, path: string, data: any = {}, user: TestUser = this.createTestUser()) {
    const headers = this.userToHeaders(user);
    return request(app.getHttpServer()).patch(path).set(headers).send(data);
  }

  /**
   * Create authenticated DELETE request
   */
  static delete(app: INestApplication, path: string, user: TestUser = this.createTestUser()) {
    const headers = this.userToHeaders(user);
    return request(app.getHttpServer()).delete(path).set(headers);
  }

  /**
   * Create user-specific test context for organization-scoped tests
   */
  static createOrgUser(orgId: string, overrides: Partial<TestUser> = {}): TestUser {
    return this.createTestUser({
      org_id: orgId,
      sub: `auth0|user-${orgId}-${Math.random().toString(36).substring(2, 11)}`,
      ...overrides
    });
  }
}
