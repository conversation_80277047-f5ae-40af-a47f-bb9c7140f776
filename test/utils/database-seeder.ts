import { Connection, Model } from 'mongoose';
import { getConnectionToken, getModelToken } from '@nestjs/mongoose';
import { Test, TestingModule } from '@nestjs/testing';
import { Types } from 'mongoose';
import { SeedDataFactory } from '../fixtures/seed-data.factory';
import Constants from 'src/common/constants';

export interface SeededData {
  organizations: any[];
  users: any[];
  serviceZones: any[];
  alertZones: any[];
  drones: any[];
  droneAuthorizations: any[];
  sensors: any[];
  eventProfiles: any[];
}

export class DatabaseSeeder {
  private connection: Connection;
  private models: Map<string, Model<any>> = new Map();

  constructor(private moduleRef: TestingModule) {
    this.connection = this.moduleRef.get<Connection>(getConnectionToken());
    this.initializeModels();
  }

  private initializeModels(): void {
    const modelNames = [
      Constants.organizations,
      'User',
      'servicezones',
      Constants.alertZones,
      Constants.drones,
      Constants.droneAuthorizations,
      Constants.sensorProfile,
      Constants.event_profile,
      Constants.event_history,
      Constants.notification,
      Constants.system_notification,
      Constants.userPreferences,
      'logs'
    ];

    modelNames.forEach(modelName => {
      try {
        const model = this.moduleRef.get<Model<any>>(getModelToken(modelName));
        this.models.set(modelName, model);
      } catch (error) {
        console.warn(`Could not get model for ${modelName}:`, error.message);
      }
    });
  }

  async clearAllCollections(): Promise<void> {
    console.log('🧹 Clearing all collections...');
    
    const collections = await this.connection.db.collections();
    
    for (const collection of collections) {
      await collection.deleteMany({});
    }
    
    console.log('✅ All collections cleared');
  }

  async seedBasicData(): Promise<SeededData> {
    console.log('🌱 Seeding basic test data...');
    
    const seededData: SeededData = {
      organizations: [],
      users: [],
      serviceZones: [],
      alertZones: [],
      drones: [],
      droneAuthorizations: [],
      sensors: [],
      eventProfiles: []
    };

    // Create organizations
    const orgModel = this.models.get(Constants.organizations);
    if (orgModel) {
      const org1 = SeedDataFactory.createOrganization({ name: 'test-org-1', display_name: 'Test Organization 1' });
      const org2 = SeedDataFactory.createOrganization({ name: 'test-org-2', display_name: 'Test Organization 2' });
      
      seededData.organizations = await orgModel.insertMany([org1, org2]);
      console.log(`✅ Created ${seededData.organizations.length} organizations`);
    }

    // Create users
    const userModel = this.models.get('User');
    if (userModel && seededData.organizations.length > 0) {
      const user1 = SeedDataFactory.createUser(seededData.organizations[0]._id, { 
        email: '<EMAIL>',
        roles: ['admin']
      });
      const user2 = SeedDataFactory.createUser(seededData.organizations[0]._id, { 
        email: '<EMAIL>',
        roles: ['user']
      });
      const user3 = SeedDataFactory.createUser(seededData.organizations[1]._id, { 
        email: '<EMAIL>',
        roles: ['admin']
      });
      
      seededData.users = await userModel.insertMany([user1, user2, user3]);
      console.log(`✅ Created ${seededData.users.length} users`);
    }

    // Create service zones
    const serviceZoneModel = this.models.get('servicezones');
    if (serviceZoneModel) {
      const serviceZone1 = SeedDataFactory.createServiceZone({ name: 'NYC Service Zone' });
      const serviceZone2 = SeedDataFactory.createServiceZone({ name: 'LA Service Zone' });
      
      seededData.serviceZones = await serviceZoneModel.insertMany([serviceZone1, serviceZone2]);
      console.log(`✅ Created ${seededData.serviceZones.length} service zones`);
    }

    // Create alert zones
    const alertZoneModel = this.models.get(Constants.alertZones);
    if (alertZoneModel && seededData.organizations.length > 0) {
      const alertZone1 = SeedDataFactory.createAlertZone(
        seededData.organizations[0]._id,
        seededData.serviceZones.map(sz => sz._id),
        { name: 'NYC Alert Zone' }
      );
      const alertZone2 = SeedDataFactory.createAlertZone(
        seededData.organizations[1]._id,
        [],
        { name: 'LA Alert Zone' }
      );
      
      seededData.alertZones = await alertZoneModel.insertMany([alertZone1, alertZone2]);
      console.log(`✅ Created ${seededData.alertZones.length} alert zones`);
    }

    return seededData;
  }

  async seedDronesAndAuthorizations(seededData: SeededData): Promise<SeededData> {
    console.log('🚁 Seeding drones and authorizations...');

    // Create drones
    const droneModel = this.models.get(Constants.drones);
    if (droneModel && seededData.organizations.length > 0) {
      const drone1 = SeedDataFactory.createDrone(seededData.organizations[0]._id.toString(), {
        display_name: 'Test Drone Alpha',
        tag: 'alpha'
      });
      const drone2 = SeedDataFactory.createDrone(seededData.organizations[0]._id.toString(), {
        display_name: 'Test Drone Beta',
        tag: 'beta'
      });
      const drone3 = SeedDataFactory.createDrone(seededData.organizations[1]._id.toString(), {
        display_name: 'Test Drone Gamma',
        tag: 'gamma'
      });

      seededData.drones = await droneModel.insertMany([drone1, drone2, drone3]);
      console.log(`✅ Created ${seededData.drones.length} drones`);
    }

    // Create drone authorizations
    const authModel = this.models.get(Constants.droneAuthorizations);
    if (authModel && seededData.drones.length > 0 && seededData.alertZones.length > 0) {
      const auth1 = SeedDataFactory.createDroneAuthorization(
        seededData.drones[0]._id,
        seededData.organizations[0]._id.toString(),
        [seededData.alertZones[0]._id],
        { authorized_by: '<EMAIL>' }
      );
      const auth2 = SeedDataFactory.createDroneAuthorization(
        seededData.drones[1]._id,
        seededData.organizations[0]._id.toString(),
        [seededData.alertZones[0]._id],
        { is_authorized: false, authorized_by: '<EMAIL>' }
      );

      seededData.droneAuthorizations = await authModel.insertMany([auth1, auth2]);
      console.log(`✅ Created ${seededData.droneAuthorizations.length} drone authorizations`);
    }

    return seededData;
  }

  async seedSensorsAndEvents(seededData: SeededData): Promise<SeededData> {
    console.log('📡 Seeding sensors and events...');

    // Create sensors
    const sensorModel = this.models.get(Constants.sensorProfile);
    if (sensorModel) {
      const sensor1 = SeedDataFactory.createSensor({
        NODE_ID: 'SENSOR_NYC_001',
        HOST_NAME: 'nyc-sensor-01',
        location: { LAT: 40.7128, LON: -74.0060, ALTITUDE: 10.5, ACCURACY: 5.0 }
      });
      const sensor2 = SeedDataFactory.createSensor({
        NODE_ID: 'SENSOR_NYC_002',
        HOST_NAME: 'nyc-sensor-02',
        location: { LAT: 40.7589, LON: -73.9851, ALTITUDE: 15.2, ACCURACY: 3.0 }
      });

      seededData.sensors = await sensorModel.insertMany([sensor1, sensor2]);
      console.log(`✅ Created ${seededData.sensors.length} sensors`);
    }

    // Create event profiles
    const eventModel = this.models.get(Constants.event_profile);
    if (eventModel && seededData.sensors.length > 0) {
      const event1 = SeedDataFactory.createEventProfile({
        DEVICE_ID: seededData.sensors[0].NODE_ID,
        DEVICE_NAME: seededData.sensors[0].HOST_NAME,
        EVENT_ID: 'EVENT_TEST_001'
      });
      const event2 = SeedDataFactory.createEventProfile({
        DEVICE_ID: seededData.sensors[1].NODE_ID,
        DEVICE_NAME: seededData.sensors[1].HOST_NAME,
        EVENT_ID: 'EVENT_TEST_002'
      });

      seededData.eventProfiles = await eventModel.insertMany([event1, event2]);
      console.log(`✅ Created ${seededData.eventProfiles.length} event profiles`);
    }

    return seededData;
  }

  async seedCompleteDataset(): Promise<SeededData> {
    console.log('🌱 Seeding complete test dataset...');

    await this.clearAllCollections();

    let seededData = await this.seedBasicData();
    seededData = await this.seedDronesAndAuthorizations(seededData);
    seededData = await this.seedSensorsAndEvents(seededData);
    

    console.log('✅ Complete dataset seeded successfully');
    console.log(`📊 Summary: ${seededData.organizations.length} orgs, ${seededData.users.length} users, ${seededData.alertZones.length} alert zones, ${seededData.drones.length} drones`);

    return seededData;
  }
}
