import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { Connection } from 'mongoose';
import { getConnectionToken, MongooseModule } from '@nestjs/mongoose';
import { DockerManager } from './docker-manager';
import { DatabaseSeeder, SeededData } from './database-seeder';
import { AuthHelper } from './auth-helper';

export interface E2ETestContext {
  app: INestApplication;
  moduleRef: TestingModule;
  connection: Connection;
  seeder: DatabaseSeeder;
  seededData?: SeededData;
}

export class TestSetup {
  static async setupE2ETest(
    additionalModules: any[] = [],
    controllers: any[] = [],
    providers: any[] = []
  ): Promise<E2ETestContext> {
    // Environment variables are set in global setup
    // Docker containers are started in global setup

    // Create test module
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        // Only import the MongoDB connection, not the model registrations
        MongooseModule.forRoot(process.env.TEST_MONGODB_CONNECTION_STRING, {
          dbName: process.env.TEST_DATABASE_NAME,
        }),
        MongooseModule.forRoot(process.env.TEST_SPECTRUM_MONGODB_CONNECTION_STRING, {
          dbName: process.env.TEST_SPECTRUM_DATABASE_NAME,
          connectionName: 'spectrum',
        }),
        ...additionalModules
      ],
      controllers: [...controllers],
      providers: [...providers],
    }).compile();

    // Create application
    const app = moduleFixture.createNestApplication();
    await app.init();

    // Get database connection
    const connection = moduleFixture.get<Connection>(getConnectionToken());

    // Create database seeder
    const seeder = new DatabaseSeeder(moduleFixture);

    return {
      app,
      moduleRef: moduleFixture,
      connection,
      seeder
    };
  }

  static async teardownE2ETest(context: E2ETestContext): Promise<void> {
    console.log('🧹 Tearing down e2e test...');

    try {
      // Clear database if seeder exists
      if (context?.seeder) {
        await context.seeder.clearAllCollections();
      }

      // Close application if it exists
      if (context?.app) {
        await context.app.close();
      }

      // Close database connection if it exists
      if (context?.connection) {
        await context.connection.close();
      }

      console.log('✅ E2E test teardown completed');
    } catch (error) {
      console.error('❌ Error during e2e test teardown:', error);
    }
  }

  static async cleanup(): Promise<void> {
    // Docker cleanup is handled in global teardown
    console.log('✅ Test cleanup completed');
  }

  static async seedTestData(context: E2ETestContext): Promise<SeededData> {
    console.log('🌱 Seeding test data...');
    const seededData = await context.seeder.seedCompleteDataset();
    context.seededData = seededData;
    return seededData;
  }

  static async waitForDatabaseConnection(context: E2ETestContext, maxWaitTime: number = 10000): Promise<void> {
    console.log('⏳ Waiting for database connection...');
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < maxWaitTime) {
      try {
        await context.connection.db.admin().ping();
        console.log('✅ Database connection established');
        return;
      } catch (error) {
        console.log('⏳ Waiting for database...');
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
    
    throw new Error(`Database connection not established within ${maxWaitTime}ms`);
  }
}
